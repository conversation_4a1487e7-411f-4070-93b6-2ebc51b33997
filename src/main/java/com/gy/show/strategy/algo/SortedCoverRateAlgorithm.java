package com.gy.show.strategy.algo;

import cn.hutool.core.bean.BeanUtil;
import com.gy.show.entity.dos.DataArea;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dto.InvokeAlgorithmDTO;
import com.gy.show.service.DataAreaService;
import com.gy.show.service.RequirementTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SortedCoverRateAlgorithm extends AbstractAlgorithmTemplate {

    @Autowired
    private DataAreaService dataAreaService;

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Override
    protected Map<String, List<Map<String, Object>>> doInvokeSelfAlgorithm(InvokeAlgorithmDTO invokeAlgorithmDTO, Map<String, List<Map<String, Object>>> result) {
        // 去重
//        distinctResult(result);

        // 按设备可执行的任务类型进行过滤
        Map<String, List<Map<String, Object>>> filterResult = filterTaskType(result);

        // 按时间进行排序筛选
        markClosestTimePeriod(filterResult, invokeAlgorithmDTO.getTask());

        // 筛选完后计算总覆盖率
//        calculateTotalCoverTime0(filterResult, invokeAlgorithmDTO);

        return filterResult;
    }

//    private void calculateTotalCoverTime0(Map<String, List<Map<String, Object>>> result, InvokeAlgorithmDTO algorithmDTO) {
//        for (Map.Entry<String, List<Map<String, Object>>> entry : result.entrySet()) {
//            String taskId = entry.getKey();
//            RequirementTask task = requirementTaskService.getById(taskId);
//            List<Map<String, Object>> equipments = entry.getValue();
//            if (equipments.size() == 0) continue;
//
//            List<Polygon> polygons = new ArrayList<>();
//            // 过滤出被标记的设备
//            for (Map<String, Object> equipment : equipments) {
//                if (equipment.get("marked").equals(1)) {
//                    polygons.add((Polygon) equipment.get("polygon"));
//                }
//                equipment.put("polygon", null);
//            }
//            if (polygons.size() == 0) continue;
//
//            // 合并区域
//            Geometry combinePolygon = GeometryCombiner.combine(polygons).union();
//
//            Map<String, LineString> lineStringMap = algorithmDTO.getLineStringMap();
//            LineString lineString = lineStringMap.get(task.getTargetRelationId());
//
//            Geometry intersection = lineString.intersection(combinePolygon);
//
//            double totalCover = intersection.getLength() / lineString.getLength();
//
//            equipments.forEach(e -> e.put("totalCover", totalCover));
//        }
//    }

//    private void calculateTotalCoverTime(Map<String, List<Map<String, Object>>> result) {
//        for (Map.Entry<String, List<Map<String, Object>>> entry : result.entrySet()) {
//            String taskId = entry.getKey();
//            List<Map<String, Object>> equipments = entry.getValue();
//            if (equipments.size() == 0) continue;
//
//            Collections.sort(equipments, Comparator.comparing(o -> LocalDateTime.parse(o.get("entryTime").toString(), DateTimeFormatter.ISO_DATE_TIME)));
//
//            LocalDateTime currentStart = LocalDateTime.parse(equipments.get(0).get("entryTime").toString(), DateTimeFormatter.ISO_DATE_TIME);
//            LocalDateTime currentEnd = LocalDateTime.parse(equipments.get(0).get("exitTime").toString(), DateTimeFormatter.ISO_DATE_TIME);
//
//            Duration totalDuration = Duration.ZERO;
//
//            for (Map<String, Object> equipment : equipments) {
//                if (equipment.get("marked").equals(0)) continue;
//
//                LocalDateTime startTime = LocalDateTime.parse(equipment.get("entryTime").toString(), DateTimeFormatter.ISO_DATE_TIME);
//                LocalDateTime endTime = LocalDateTime.parse(equipment.get("exitTime").toString(), DateTimeFormatter.ISO_DATE_TIME);
//
//                if (startTime.isAfter(currentEnd)) {
//                    totalDuration = totalDuration.plus(Duration.between(currentStart, currentEnd));
//
//                    currentStart = startTime;
//                    currentEnd = endTime;
//                } else {
//                    currentEnd = currentEnd.isAfter(endTime) ? currentEnd : endTime;
//                }
//            }
//
//            totalDuration = totalDuration.plus(Duration.between(currentStart, currentEnd));
//
//            // 查询任务
//            RequirementTask task = requirementTaskService.getById(taskId);
//            long totalTime = Duration.between(task.getStartTime(), task.getEndTime()).getSeconds();
//            // 放第一个
//            double totalCover = (double) totalDuration.getSeconds() / totalTime;
//            equipments.get(0).put("totalCover", totalCover);
//            log.info("总覆盖率：{}秒", totalDuration.getSeconds());
//        }
//    }

//    private void markClosestTimePeriod(Map<String, List<Map<String, Object>>> areaResult, RequirementTask task) {
//        List<Map<String, Object>> mapList = areaResult.values()
//                .stream()
//                .flatMap(List::stream)
//                .filter(m -> m.get("entryTime") != null)
//                .collect(Collectors.toList());
//
//        Collections.sort(mapList, Comparator.comparing(o -> LocalDateTime.parse(o.get("entryTime").toString(), DateTimeFormatter.ISO_DATE_TIME)));
//
//        List<Map<String, Object>> result = new ArrayList<>(mapList.size());
//
//        LocalDateTime currentEnd = task.getStartTime();
//        for (int i = 0; i < mapList.size(); i++) {
//            Map<String, Object> current = mapList.get(i);
//            LocalDateTime periodStart = LocalDateTime.parse(current.get("entryTime").toString(), DateTimeFormatter.ISO_DATE_TIME);
//            LocalDateTime periodEnd = LocalDateTime.parse(current.get("exitTime").toString(), DateTimeFormatter.ISO_DATE_TIME);
//
//            // 合并相同开始时间的时间段，取结束时间的并集
//            while (i + 1 < mapList.size() &&  LocalDateTime.parse(mapList.get(i + 1).get("entryTime").toString(), DateTimeFormatter.ISO_DATE_TIME).equals(periodStart)) {
//                periodEnd = periodEnd.isAfter(LocalDateTime.parse(mapList.get(i + 1).get("exitTime").toString(), DateTimeFormatter.ISO_DATE_TIME)) ? periodEnd
//                        : LocalDateTime.parse(mapList.get(i + 1).get("exitTime").toString(), DateTimeFormatter.ISO_DATE_TIME);
//                i++;
//            }
//
//            // 检查是否有重叠部分
//            if (periodStart.isBefore(currentEnd)) {
//                // 如果有重叠部分, 截取非重叠部分
//                if (periodEnd.isAfter(currentEnd)) {
//                    current.put("entryTime", currentEnd);
//                    current.put("exitTime", periodEnd);
//                    current.put("marked", 1);
//                    result.add(current);
//                    currentEnd = periodEnd;
//                } else {
//                    current.put("marked", 0);
//                    result.add(current);
//                }
//            } else {
//                // 没有重叠， 直接添加时间段
//                current.put("marked", 1);
//                current.put("entryTime", periodStart);
//                current.put("exitTime", periodEnd);
//                result.add(current);
//                currentEnd = periodEnd;
//            }
//
//            if (currentEnd.isAfter(task.getEndTime()) || currentEnd.isEqual(task.getEndTime())) {
//                break;
//            }
//        }
//
//        areaResult.put(task.getId(), result);
//
//    }

    private void markClosestTimePeriod(Map<String, List<Map<String, Object>>> areaResult, RequirementTask task) {
        // 获取所有带有entryTime的记录并按entryTime排序
        List<Map<String, Object>> mapList = areaResult.values()
                .stream()
                .flatMap(List::stream)
                .filter(m -> m.get("entryTime") != null)
                .collect(Collectors.toList());

        Collections.sort(mapList, Comparator.comparing(o -> LocalDateTime.parse(o.get("entryTime").toString(), DateTimeFormatter.ISO_DATE_TIME)));

        List<Map<String, Object>> result = new ArrayList<>(mapList.size());

        LocalDateTime currentEnd = task.getStartTime();
        for (int i = 0; i < mapList.size(); i++) {
            Map<String, Object> current = mapList.get(i);
            LocalDateTime periodStart = LocalDateTime.parse(current.get("entryTime").toString(), DateTimeFormatter.ISO_DATE_TIME);
            LocalDateTime periodEnd = LocalDateTime.parse(current.get("exitTime").toString(), DateTimeFormatter.ISO_DATE_TIME);

            // 如果当前时间段在任务结束时间之后，标记无效
            if (periodStart.isAfter(task.getEndTime())) {
                current.put("marked", 0);
                result.add(current);
                continue;
            }

            // 如果时间段和currentEnd有重叠，处理重叠部分
            if (periodStart.isBefore(currentEnd)) {
                // 如果periodEnd超出currentEnd，说明部分重叠，截取非重叠部分
                if (periodEnd.isAfter(currentEnd)) {
                    // 截取9点后的时间段，即非重叠部分
                    LocalDateTime newEntryTime = currentEnd;
                    LocalDateTime newExitTime = periodEnd;

                    current.put("entryTime", newEntryTime);
                    current.put("exitTime", newExitTime);
                    current.put("marked", 1);  // 标记为有效
                    result.add(current);

                    currentEnd = newExitTime;  // 更新currentEnd到最新的时间
                } else {
                    // 如果完全被覆盖，标记为无效
                    current.put("marked", 0);
                    result.add(current);
                }
            } else {
                // 没有重叠的情况下，直接标记为有效
                current.put("marked", 1);
                current.put("entryTime", periodStart);
                current.put("exitTime", periodEnd);
                result.add(current);

                currentEnd = periodEnd;  // 更新currentEnd到当前的结束时间
            }

            // 如果当前时间超过任务结束时间，结束循环
            if (currentEnd.isAfter(task.getEndTime())) {
                break;
            }
        }

        // 保持结果的原始数量，最终将结果放回areaResult中
        areaResult.put(task.getId(), result);
    }





    /**
     * 按资源域进行分组
     * @param result
     * @return
     */
    private Map<String, List<Map<String, Object>>> groupByArea(Map<String, List<Map<String, Object>>> result) {
        Map<String, List<Map<String, Object>>> areaResult = new HashMap<>();

        for (Map.Entry<String, List<Map<String, Object>>> entry : result.entrySet()) {
            Map<String, List<Map<String, Object>>> groupByAreaId = entry.getValue().stream()
                    .collect(Collectors.groupingBy(map -> map.get("areaId").toString()));

            List<Map<String, Object>> areaCoverList = new ArrayList<>();
            for (Map.Entry<String, List<Map<String, Object>>> entry1 : groupByAreaId.entrySet()) {
                List<Map<String, Object>> equipments = entry1.getValue();
                double areaCover = 0;
                String entryTime = null, exitTime = null;
                for (Map<String, Object> equipment : equipments) {
                    Double coverRate = Double.parseDouble(equipment.get("coverRate").toString());
                    if (coverRate == 1.0) {
                        areaCover = 1.0;
                        break;
                    }

                    // 取最大的覆盖率
                    if (coverRate > areaCover) {
                        areaCover = coverRate;
                        entryTime = equipment.get("entryTime").toString();
                        exitTime = equipment.get("exitTime").toString();
                        continue;
                    }
                }
                DataArea dataArea = dataAreaService.getById(entry1.getKey());
                Map<String, Object> map = BeanUtil.beanToMap(dataArea);
                map.put("areaCover", areaCover);
                map.put("equipments", equipments);
                map.put("entryTime", entryTime);
                map.put("exitTime", exitTime);
                areaCoverList.add(map);
            }

            areaResult.put(entry.getKey(), areaCoverList);
        }

        return areaResult;
    }
}
