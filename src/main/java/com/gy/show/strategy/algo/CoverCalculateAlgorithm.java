package com.gy.show.strategy.algo;

import com.gy.show.entity.dto.InvokeAlgorithmDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Component
public class CoverCalculateAlgorithm extends AbstractAlgorithmTemplate {

    @Override
    protected Map<String, List<Map<String, Object>>> doInvokeSelfAlgorithm(InvokeAlgorithmDTO invokeAlgorithmDTO, Map<String, List<Map<String, Object>>> result) {
        // 根据ID进行去重
        distinctResult(result);

        Set<Map.Entry<String, List<Map<String, Object>>>> entries = result.entrySet();
        for (Map.Entry<String, List<Map<String, Object>>> entry : entries) {
            entry.getValue().forEach(e -> e.put("polygon", null));
        }
        return result;
    }

}
