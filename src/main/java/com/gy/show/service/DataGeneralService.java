package com.gy.show.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dto.BasicDataDTO;
import com.gy.show.entity.dto.DataGeneralDTO;

import java.util.List;
import java.util.Map;

public interface DataGeneralService extends IService<DataGeneral> {

    /**
     * 根据ID获取目标数据信息
     *
     * @param id 目标数据ID
     * @return 目标数据信息
     */
    DataGeneral getDataGeneralById(String id);

    /**
     * 创建目标数据信息
     *
     * @param dataGeneral 目标数据信息
     * @return 创建后的目标数据信息
     */
    DataGeneral createDataGeneral(DataGeneral dataGeneral);

    /**
     * 更新目标数据信息
     *
     * @param id          目标数据ID
     * @param newDataGeneral 更新后的目标数据信息
     * @return 更新后的目标数据信息
     */
    DataGeneral updateDataGeneral(String id, DataGeneral newDataGeneral);

    /**
     * 删除目标数据信息
     *
     * @param id 目标数据ID
     */
    void deleteDataGeneral(String id);

    /**
     * 分页获取所有目标数据信息
     *
     * @param page    当前页码，默认为1
     * @param size    每页记录数，默认为10
     * @param keyword 关键字，模糊查询模型ID或表名或表描述
     * @return 目标数据信息分页列表
     */
    IPage<DataGeneral> getAllDataGenerals(int page, int size, String keyword);

    List<DataGeneralDTO> getDataByArea(String areaId);

    IPage getDataByType(String dataTypeId, String areaId, String keyword, IPage page);

    List<Map<String, Object>> getFieldByType(String dataTypeId);

    void saveData(BasicDataDTO basicDataDTO);

    void deleteData(String dataTypeId, String dataId);

    void updateData(BasicDataDTO basicDataDTO);

    Map<String, Object> getDataDetail(String dataTypeId, String dataId);

    List<DataGeneralDTO>  getType(Integer areaType);

    Map<String, IPage<Map<String, Object>>> queryIdleSource(IPage page, String areaId);

    List<DataGeneralDTO> queryEquipmentType(Integer type);

    Object pageEquipmentByType(IPage page, Integer type, String areaId, Integer category);


    Object pageStatsEquipment(IPage<Object> page, String generaId);

    List<DataGeneral> queryLittleType(Integer type);
}
