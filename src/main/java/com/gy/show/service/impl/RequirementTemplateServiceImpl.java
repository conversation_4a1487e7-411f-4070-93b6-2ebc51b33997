package com.gy.show.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.DataFile;
import com.gy.show.entity.dos.RequirementTemplate;
import com.gy.show.entity.dos.TemplateBusinessRelation;
import com.gy.show.entity.dos.TemplateTargetRelation;
import com.gy.show.entity.dto.RequirementTemplateDTO;
import com.gy.show.entity.dto.TemplateBusinessRelationDTO;
import com.gy.show.entity.dto.TemplateTargetRelationDTO;
import com.gy.show.mapper.DataFileMapper;
import com.gy.show.mapper.RequirementTemplateMapper;
import com.gy.show.service.RequirementTemplateService;
import com.gy.show.service.TemplateBusinessRelationService;
import com.gy.show.service.TemplateTargetRelationService;
import com.gy.show.util.ConvertUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class RequirementTemplateServiceImpl extends ServiceImpl<RequirementTemplateMapper, RequirementTemplate> implements RequirementTemplateService {

    @Resource
    private DataFileMapper dataFileMapper;
    @Resource
    private TemplateBusinessRelationService templateBusinessRelationService;
    @Resource
    private TemplateTargetRelationService templateTargetRelationService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RequirementTemplate addRequirementTemplate(RequirementTemplateDTO requirementTemplateDTO) {
        RequirementTemplate template = requirementTemplateDTO.convert();

        save(template);

        if (requirementTemplateDTO.getTargetInfos() != null) {
            for (TemplateTargetRelationDTO dto : requirementTemplateDTO.getTargetInfos()) {
                String targetRelationId = IdWorker.getIdStr();
                dto.setId(targetRelationId);
                dto.setRTemplateId(template.getId());
                if (dto.getTasks() != null && dto.getTasks().size() != 0) {
                    List<TemplateBusinessRelation> businessRelations = new ArrayList<>();
                    for (TemplateBusinessRelationDTO relationDto : dto.getTasks()) {
                        String secondGradeId = IdWorker.getIdStr();
                        for (Integer taskTypeId : relationDto.getTaskType()) {
                            TemplateBusinessRelation relation = relationDto.convert();
                            relation.setSecondGradeId(secondGradeId);
                            relation.setTaskType(taskTypeId);
                            relation.setId(IdWorker.getIdStr());
                            relation.setTargetRelationId(targetRelationId);
                            businessRelations.add(relation);
                        }
                    }
                    templateBusinessRelationService.saveBatch(businessRelations);
                }
                TemplateTargetRelation convert = dto.convert();
                templateTargetRelationService.save(convert);
            }
        }

        return template;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateRequirementTemplate(RequirementTemplateDTO requirementTemplate) {
        RequirementTemplate template = requirementTemplate.convert();

        updateById(template);
        if (requirementTemplate.getTargetInfos() != null) {
            for (TemplateTargetRelationDTO dto : requirementTemplate.getTargetInfos()) {
                TemplateTargetRelation convert = dto.convert();
                // 先删除历史目标再去新增
                templateTargetRelationService.remove(Wrappers.<TemplateTargetRelation>lambdaQuery().eq(TemplateTargetRelation::getRTemplateId, template.getId()));

                convert.setRTemplateId(template.getId());
                templateTargetRelationService.saveOrUpdate(convert);
                if (dto.getTasks() != null) {
                    List<TemplateBusinessRelation> businessRelations = new ArrayList<>();
                    for (TemplateBusinessRelationDTO task : dto.getTasks()) {
                        String secondGradeId = StringUtils.isNotBlank(task.getSecondGradeId()) ? task.getSecondGradeId() : IdWorker.getIdStr();
                        templateBusinessRelationService.remove(new QueryWrapper<TemplateBusinessRelation>().eq("second_grade_id",secondGradeId));
                        for (Integer taskTypeId : task.getTaskType()) {
                            TemplateBusinessRelation businessRelation = task.convert();
                            businessRelation.setTaskType(taskTypeId);
                            businessRelation.setSecondGradeId(secondGradeId);
                            businessRelation.setTargetRelationId(convert.getId());
                            businessRelation.setId(null);
                            businessRelations.add(businessRelation);
                        }
                    }
                    templateBusinessRelationService.saveBatch(businessRelations);
                }
            }
        }
    }

    @Override
    public IPage<RequirementTemplateDTO> getAllRequirementTemplate(Integer pageNum, Integer pageSize, String keyword) {
        IPage<RequirementTemplate> templateIPage = page(new Page<>(pageNum, pageSize), Wrappers.<RequirementTemplate>lambdaQuery()
                .like(StringUtils.isNotBlank(keyword), RequirementTemplate::getTemplateName, keyword)
                .orderByDesc(RequirementTemplate::getCreateTime));

        IPage<RequirementTemplateDTO> resultPage = ConvertUtil.buildPage(templateIPage);
        List<RequirementTemplateDTO> records = resultPage.getRecords();
        for (RequirementTemplateDTO record : records) {
            if (StringUtils.isNotBlank(record.getFileId())) {
                DataFile file = dataFileMapper.selectById(record.getFileId());
                record.setFile(file);
            }
        }
        return resultPage;
    }

    @Override
    public RequirementTemplateDTO getRequirementTemplateById(String id) {
        RequirementTemplate template = getById(id);
        RequirementTemplateDTO templateDTO = template.convert();

        if (StringUtils.isNotBlank(templateDTO.getFileId())) {
            DataFile file = dataFileMapper.selectById(templateDTO.getFileId());
            templateDTO.setFile(file);
        }

        List<TemplateTargetRelation> templateTargetRelations = templateTargetRelationService.list(new QueryWrapper<TemplateTargetRelation>().eq("r_template_id", id));
        List<TemplateTargetRelationDTO> targetRelationDTOS = templateTargetRelations.stream().map(targetRelation -> {
            TemplateTargetRelationDTO convert = targetRelation.convert();
            List<TemplateBusinessRelation> businessRelations = templateBusinessRelationService.list(new QueryWrapper<TemplateBusinessRelation>().eq("target_relation_id", targetRelation.getId()));
            Map<String, List<TemplateBusinessRelation>> secondGradeMap = businessRelations.stream().collect(Collectors.groupingBy(br -> br.getSecondGradeId()));
            List<TemplateBusinessRelationDTO> dtoCollect = new ArrayList<>();
            for (String secondGradeId : secondGradeMap.keySet()) {
                TemplateBusinessRelation relation = secondGradeMap.get(secondGradeId).get(0);
                TemplateBusinessRelationDTO dto = relation.convert();
                List<Integer> taskTypes = secondGradeMap.get(secondGradeId).stream().map(br -> br.getTaskType()).collect(Collectors.toList());
                dto.setTaskType(taskTypes);
                dtoCollect.add(dto);
            }
            convert.setTasks(dtoCollect);
            return convert;
        }).collect(Collectors.toList());
        templateDTO.setTargetInfos(targetRelationDTOS);
        return templateDTO;
    }

    @Override
    public void deleteRequirementTemplate(String id) {
        removeById(id);
        List<TemplateTargetRelation> targetRelations = templateTargetRelationService.list(new QueryWrapper<TemplateTargetRelation>().eq("r_template_id", id));
        List<String> targetRelationIds = targetRelations.stream().map(TemplateTargetRelation::getId).collect(Collectors.toList());
        templateTargetRelationService.removeByIds(targetRelationIds);
        templateBusinessRelationService.remove(new QueryWrapper<TemplateBusinessRelation>().in("target_relation_id", targetRelationIds));
    }

}
