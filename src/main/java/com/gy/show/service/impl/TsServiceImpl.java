package com.gy.show.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dto.WsMessageDTO;
import com.gy.show.enums.WebSocketTypeEnum;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.mapper.DataGeneralMapper;
import com.gy.show.service.TsService;
import com.gy.show.util.GISUtils;
import com.gy.show.ws.FullViewTsServer;
import com.gy.show.ws.MyWebSocketClient;
import org.java_websocket.enums.ReadyState;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.awt.geom.Point2D;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TsServiceImpl implements TsService {


    @Autowired
    private DataGeneralMapper dataGeneralMapper;

    @Resource
    private CommonMapper commonMapper;

    @Resource
    private FullViewTsServer fullViewTsServer;

    @Override
    public void simulateTsData() {
        new Thread(() -> {
            List<Map<String, Object>> allRecords = new ArrayList<>();
            List<DataGeneral> dataGenerals = dataGeneralMapper.selectList(Wrappers.<DataGeneral>lambdaQuery().in(DataGeneral::getId, Arrays.asList(5, 8)));
//            List<DataGeneral> dataGenerals = dataGeneralMapper.selectList(null);
            for (DataGeneral dataGeneral : dataGenerals) {
                IPage<Map<String, Object>> data = commonMapper.getData(new Page<>(-1, -1), dataGeneral.getTableName(), null, null, null);
                List<Map<String, Object>> records = data.getRecords();
                allRecords.addAll(records.stream().map(map -> {
                    map.put("dataType", dataGeneral.getDataType());
                    return map;
                }).collect(Collectors.toList()));
            }
            List<List<Point2D.Double>> allPoints = new ArrayList<>();
            int maxSize = 0;
            for (int i = 0; i < allRecords.size(); i++) {
                List<Point2D.Double> keyPoint = keyPoint();
                List<Point2D.Double> allPoint = GISUtils.insertPoint(keyPoint, 10000);
                allPoints.add(allPoint);
                if (allPoint.size() > maxSize) {
                    maxSize = allPoint.size();
                }
            }
            for (int i = 0; i < maxSize; i++) {
                WsMessageDTO dto = new WsMessageDTO();
                List<Object> dataList = new ArrayList<>();
//                for (int j = 0; j < allRecords.size(); j++) {
                for (int j = 0; j < 5; j++) {
                    List<Point2D.Double> list = allPoints.get(j);
                    if (list.size() > i) {
                        Map<String, Object> msg = new HashMap<>();
                        msg.put("x", list.get(i).getX());
                        msg.put("y", list.get(i).getY());
                        msg.put("z", 0);
                        msg.put("dataType", allRecords.get(j).get("dataType").toString());
                        msg.put("dataTypeId", allRecords.get(j).get("generalId").toString());
                        msg.put("dataId", allRecords.get(j).get("id").toString());
                        dataList.add(msg);
                    }
                }
                dto.setData(dataList);
                dto.setType(WebSocketTypeEnum.FULLVIEW.getCode());
                fullViewTsServer.sendAll(JSON.toJSONString(dto));
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }

    @Override
    public void simulateTsData0(String requirementId) {
        // 查询该需求下所有任务

        // 查询所有相关联的资源

        // 查询所有相关联的目标

        // 计算执行任务的百分比

    }

    public void socketClientConnection(String ip, String port, String clientId) {
        MyWebSocketClient client = null;
        try {
            client = new MyWebSocketClient("ws://" + ip + ":" + port + "/wrpt/ws/fullViewTs/" + clientId);
            client.connect();
            while (client.getReadyState() != ReadyState.OPEN) {
                System.out.println("连接状态：" + client.getReadyState());
                Thread.sleep(100);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 随机生成经纬度
     *
     * @return
     */
    private List<Point2D.Double> keyPoint() {
        List<Point2D.Double> keyPoint = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            double lon = Math.random() * (130 - 80 + 1) + 80; //80-130
            double lat = Math.random() * (40 - 20 + 1) + 20; //20-40
            keyPoint.add(new Point2D.Double(lon, lat));
        }
        return keyPoint;
    }
}
