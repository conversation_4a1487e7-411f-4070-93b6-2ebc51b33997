package com.gy.show.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dos.RequirementTargetTrack;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dos.TaskTargetRelation;
import com.gy.show.entity.dto.RequirementTargetTrackDTO;
import com.gy.show.entity.dto.TaskTargetRelationDTO;
import com.gy.show.entity.vo.PointVO;
import com.gy.show.enums.DataTypeEnum;
import com.gy.show.mapper.TaskTargetRelationMapper;
import com.gy.show.service.*;
import com.gy.show.util.GISUtils;
import com.gy.show.util.GeoInterpolation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TaskTargetServiceImpl extends ServiceImpl<TaskTargetRelationMapper, TaskTargetRelation> implements TaskTargetService {

    @Autowired
    private RequirementTargetTrackService requirementTargetTrackService;

    @Autowired
    private DataGeneralService dataGeneralService;

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Override
    public TaskTargetRelationDTO listByTask(String taskId) {
        RequirementTask task = requirementTaskService.getById(taskId);
//        List<TaskTargetRelation> targets = list(Wrappers.<TaskTargetRelation>lambdaQuery().eq(TaskTargetRelation::getTaskId, taskId).eq(TaskTargetRelation::getType, 1));

        TaskTargetRelation targetRelation = getById(task.getTargetRelationId());
        if (targetRelation == null) {
            return new TaskTargetRelationDTO();
        }
        // 查询目标航迹
        Map<String, List<RequirementTargetTrackDTO>> targetTrack = queryTargetTrack(Collections.singletonList(targetRelation));


        TaskTargetRelationDTO convert = targetRelation.convert();
        List<RequirementTargetTrackDTO> track = targetTrack.get(targetRelation.getId());

        convert.setTargetTrack(track);

        DataGeneral dataGeneral = dataGeneralService.getById(convert.getGeneralId());
        convert.setDataType(dataGeneral.getDataType());
        convert.setDataTypeValue(DataTypeEnum.getEnumByCode(dataGeneral.getDataType()).getMessage());

        // 计算任务覆盖的进出经纬度点
        LinkedList<double[]> points = calculateTaskPoint(task, track, targetRelation);
        convert.setTaskCoverPoint(points);

        // 转换
        return convert;
    }
    
    public LinkedList<double[]> calculateTaskPoint(RequirementTask task, List<RequirementTargetTrackDTO> track, TaskTargetRelation targetRelation) {
        LinkedList<double[]> points = null;
        if (targetRelation.getStartTime().isBefore(task.getStartTime()) || targetRelation.getStartTime().isEqual(task.getStartTime())) {
            double[] startPoint = calculateTaskCoverPoint(targetRelation.getStartTime(), task.getStartTime(), track);

            double[] endPoint = calculateTaskCoverPoint(targetRelation.getStartTime(), task.getEndTime(), track);

            points = calculateTempPoint(task, track, targetRelation);

            // 如果任务开始时间就是航迹的起点的话，这里需要去重,所以只有在不相等的时候才添加这个起始点
            if (!targetRelation.getStartTime().isEqual(task.getStartTime())) {
                points.addFirst(startPoint);
            }
            points.addLast(endPoint);
        }
        
        return points;
    }

    private LinkedList<double[]> calculateTempPoint(RequirementTask task, List<RequirementTargetTrackDTO> track, TaskTargetRelation targetRelation) {
        long startTime = Duration.between(targetRelation.getStartTime(), task.getStartTime()).getSeconds();
        long endTime = Duration.between(targetRelation.getStartTime(), task.getEndTime()).getSeconds();

        LinkedList<double[]> points = new LinkedList<>();
        for (RequirementTargetTrackDTO trackDTO : track) {
            if (startTime <= trackDTO.getTime() && endTime >= trackDTO.getTime()) {
                points.add(new double[]{trackDTO.getLatitude().doubleValue(), trackDTO.getLongitude().doubleValue(), trackDTO.getTime()}) ;
            }
        }
        return points;
    }

    private double[] calculateTaskCoverPoint(LocalDateTime startTime, LocalDateTime startTime1, List<RequirementTargetTrackDTO> track) {
        Duration duration = Duration.between(startTime, startTime1);

        long anyPointTime = duration.getSeconds();
        // 判断这个时间点位于哪两个关键点之间

        RequirementTargetTrackDTO prePoint = null;
        RequirementTargetTrackDTO nextPoint = null;
        for (RequirementTargetTrackDTO trackDTO : track) {
            if (anyPointTime >= trackDTO.getTime()) {
                prePoint = trackDTO;
            } else {
                nextPoint = trackDTO;
                break;
            }
        }

        if (nextPoint == null && anyPointTime >= track.get(track.size() - 1).getTime()) {
            nextPoint = track.get(track.size() - 1);
            return new double[]{nextPoint.getLatitude().doubleValue(), nextPoint.getLongitude().doubleValue(), nextPoint.getTime()};
        }

        double[] point = GISUtils.calculatePointAtTime(prePoint.getLatitude().doubleValue(), prePoint.getLongitude().doubleValue(),
                nextPoint.getLatitude().doubleValue(), nextPoint.getLongitude().doubleValue(), prePoint.getSpeed().doubleValue(),
                nextPoint.getSpeed().doubleValue(), prePoint.getTime(), nextPoint.getTime(), anyPointTime);

        double[] newPoint = Arrays.copyOf(point, point.length + 1);
        newPoint[newPoint.length - 1] = anyPointTime;

        return newPoint;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void storeTarget(TaskTargetRelationDTO taskTargetRelationDTO) {
        // 存储目标主表
        TaskTargetRelation targetRelation = taskTargetRelationDTO.convert();
        save(targetRelation);

        // 存储航迹表
        List<RequirementTargetTrack> tracks = taskTargetRelationDTO.getTargetTrack()
                .stream()
                .map(track -> {
                    RequirementTargetTrack t = track.convert();
                    t.setRelationId(targetRelation.getId());
                    return t;
                })
                .collect(Collectors.toList());

        requirementTargetTrackService.saveBatch(tracks);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeTrack(String relationId) {
        boolean count = requirementTargetTrackService.remove(Wrappers.<RequirementTargetTrack>lambdaQuery().eq(RequirementTargetTrack::getRelationId, relationId));
        log.info("删除目标航迹成功，relationId:{}, 删除航迹点数：{}", relationId, count);
}

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTarget(TaskTargetRelationDTO taskTargetRelationDTO) {
        // 修改目标关联表
        TaskTargetRelation targetRelation = taskTargetRelationDTO.convert();
        updateById(targetRelation);

        // 修改目标航迹表
        List<RequirementTargetTrackDTO> targetTrack = taskTargetRelationDTO.getTargetTrack();
        if (CollUtil.isNotEmpty(targetTrack)) {
            List<RequirementTargetTrack> tracks = targetTrack.stream()
                    .map(RequirementTargetTrackDTO::convert)
                    .collect(Collectors.toList());

            requirementTargetTrackService.remove(Wrappers.<RequirementTargetTrack>lambdaQuery().eq(RequirementTargetTrack::getRelationId, targetTrack.get(0).getRelationId()));

            requirementTargetTrackService.saveOrUpdateBatch(tracks);
        }
    }

    @Override
    public List<TaskTargetRelation> listByTaskIds(List<String> taskIds) {
        if (CollUtil.isEmpty(taskIds)) return Collections.EMPTY_LIST;
        return list(Wrappers.<TaskTargetRelation>lambdaQuery().in(TaskTargetRelation::getTaskId, taskIds).eq(TaskTargetRelation::getType, 1));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeTargetAndTrack(String taskId, String targetId) {
        // 先删除关联航迹
        List<TaskTargetRelation> targets = list(Wrappers.<TaskTargetRelation>lambdaQuery().eq(TaskTargetRelation::getTaskId, taskId)
                .eq(TaskTargetRelation::getTargetId, targetId));

        // 以防一个任务对同一个目标id有多个,这里做批量删除
        List<String> ids = targets.stream()
                .map(TaskTargetRelation::getId)
                .collect(Collectors.toList());

        // 删除关联航迹
        requirementTargetTrackService.remove(Wrappers.<RequirementTargetTrack>lambdaQuery().in(RequirementTargetTrack::getRelationId, ids));

        // 删除任务目标关联表
        removeByIds(ids);
    }

    private Map<String, List<RequirementTargetTrackDTO>> queryTargetTrack(List<TaskTargetRelation> targets) {
        List<String> relationIds = targets.stream()
                .map(TaskTargetRelation::getId)
                .collect(Collectors.toList());

        // 查询目标航迹表
        if (CollUtil.isEmpty(relationIds)) {
            return new HashMap<>();
        }
        List<RequirementTargetTrack> targetTracks = requirementTargetTrackService.list(Wrappers.<RequirementTargetTrack>lambdaQuery()
                .in(RequirementTargetTrack::getRelationId, relationIds));

        // 将航迹按照目标id来进行分组
        Map<String, List<RequirementTargetTrackDTO>> groupByTarget = targetTracks.stream()
                .collect(Collectors.groupingBy(RequirementTargetTrack::getRelationId,
                        Collectors.mapping(RequirementTargetTrack::convert, Collectors.toList())));

        // 插值计算
        Map<String, List<RequirementTargetTrackDTO>> result = new HashMap<>(groupByTarget.size());
        // 生成插值点的数量
        int numPoints = 10;
        for (Map.Entry<String, List<RequirementTargetTrackDTO>> entry : groupByTarget.entrySet()) {
            List<RequirementTargetTrackDTO> tracks = entry.getValue();
            List<RequirementTargetTrackDTO> points = new ArrayList<>();
            for (int i = 0; i < tracks.size(); i++) {
                if (i != tracks.size() - 1) {
                    RequirementTargetTrackDTO start = tracks.get(i);
                    RequirementTargetTrackDTO end = tracks.get(i + 1);

                    double[] pointA = {start.getLatitude().doubleValue(), start.getLongitude().doubleValue(), start.getAltitude().doubleValue()};
                    double[] pointB = {end.getLatitude().doubleValue(), end.getLongitude().doubleValue(), end.getAltitude().doubleValue()};

                    List<double[]> interpolatedPoints = GeoInterpolation.createInterpolationPoints(pointA, pointB, numPoints);

                    List<RequirementTargetTrackDTO> convertPoint = interpolatedPoints.stream()
                            .map(doubles -> {
                                RequirementTargetTrackDTO point = new RequirementTargetTrackDTO();
                                BeanUtil.copyProperties(start, point);

                                point.setLatitude(BigDecimal.valueOf(doubles[0]));
                                point.setLongitude(BigDecimal.valueOf(doubles[1]));

                                return point;
                            }).collect(Collectors.toList());

                    points.addAll(convertPoint);
                }
            }

            result.put(entry.getKey(), points);
        }

        return result;
    }
}
