package com.gy.show.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.common.ServiceException;
import com.gy.show.entity.dos.*;
import com.gy.show.entity.dto.BasicDataDTO;
import com.gy.show.entity.dto.DataGeneralDTO;
import com.gy.show.enums.DataTypeEnum;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.mapper.DataFileMapper;
import com.gy.show.mapper.DataGeneralAreaRelationMapper;
import com.gy.show.mapper.DataGeneralMapper;
import com.gy.show.service.*;
import com.gy.show.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DataGeneralServiceImpl extends ServiceImpl<DataGeneralMapper, DataGeneral> implements DataGeneralService {

    @Autowired
    private DataGeneralAreaRelationMapper dataGeneralAreaRelationMapper;

    @Autowired
    private DataGeneralMapper dataGeneralMapper;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private SysDictionaryService sysDictionaryService;

    @Autowired
    private DataEquipmentOccupancyService dataEquipmentOccupancyService;

    @Autowired
    private DataAreaService dataAreaService;

    @Autowired
    private TaskTargetRelationService taskTargetRelationService;

    @Autowired
    private DataFileMapper dataFileMapper;

    /**
     * 数据类型 1 地基平台 2 天基平台 3 空基平台 4 无人机 5 无人艇 6 无人车 7 弹
     */
    private Map<Integer, List<Integer>> mappingType = new HashMap<>();

    @PostConstruct
    public void init() {
        mappingType.put(1, Arrays.asList(1, 2, 3, 8));
        mappingType.put(2, Arrays.asList(4, 5, 6, 7));
    }

    public DataGeneral getDataGeneralById(String id) {
        return dataGeneralMapper.selectById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public DataGeneral createDataGeneral(DataGeneral dataGeneral) {
        dataGeneralMapper.insert(dataGeneral);
        return dataGeneral;
    }

    @Transactional(rollbackFor = Exception.class)
    public DataGeneral updateDataGeneral(String id, DataGeneral newDataGeneral) {
        newDataGeneral.setId(id);
        dataGeneralMapper.updateById(newDataGeneral);
        return newDataGeneral;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteDataGeneral(String id) {
        dataGeneralMapper.deleteById(id);
    }

    public IPage<DataGeneral> getAllDataGenerals(int page, int size, String keyword) {
        LambdaQueryWrapper<DataGeneral> queryWrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isEmpty(keyword)) {
            queryWrapper.and(wrapper -> wrapper.like(DataGeneral::getModelId, keyword)
                    .or().like(DataGeneral::getTableName, keyword)
                    .or().like(DataGeneral::getTableComment, keyword));
        }
        queryWrapper.orderByDesc(DataGeneral::getCreateTime);
        return dataGeneralMapper.selectPage(new Page<>(page, size), queryWrapper);
    }

    @Override
    public List<DataGeneralDTO> getDataByArea(String areaId) {
        // 查询该资源域下有关联的类型
        List<DataGeneralAreaRelation> dataGeneralAreaRelations = dataGeneralAreaRelationMapper.selectList(
                Wrappers.<DataGeneralAreaRelation>lambdaQuery().select(DataGeneralAreaRelation::getGeneralId).eq(DataGeneralAreaRelation::getAreaId, areaId));

        List<String> generalIds = dataGeneralAreaRelations.stream()
                .map(DataGeneralAreaRelation::getGeneralId)
                .collect(Collectors.toList());

        // 查询基础数据主表
        Collection<DataGeneral> dataGenerals = listByIds(generalIds);

        return dataGenerals.stream()
                .sorted(Comparator.comparing(DataGeneral::getDataType))
                .map(dataGeneral -> {
                    DataGeneralDTO dataGeneralDTO = dataGeneral.convert();
                    // 转换数据类型枚举
                    dataGeneralDTO.setDataTypeValue(DataTypeEnum.getEnumByCode(dataGeneralDTO.getDataType()).getMessage());
                    return dataGeneralDTO;
                }).collect(Collectors.toList());
    }

    @Override
    public IPage getDataByType(String dataTypeId, String areaId, String keyword, IPage page) {
        // 查询表头
        DataGeneral general = getById(dataTypeId);

        if (general == null) {
            throw new ServiceException("未查询到该实体模型");
        }

        List<String> qks = new ArrayList<>();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(keyword)) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(general.getQueryKeys())) {
                String[] split = general.getQueryKeys().split(",");

                CollUtil.addAll(qks, split);
            } else {
                keyword = null;
                log.warn("该表{}未配置查询关键字，需要配置后才能进行模糊匹配查询", general.getTableName());
            }
        }

        Map<String, Object> args = new HashMap<>();
        // 查询参数
        if (org.apache.commons.lang3.StringUtils.isNotBlank(areaId)) {
            args.put("area_id", areaId);
        }

        IPage<Map<String, Object>> resultPage = commonMapper.getData(page, general.getTableName(), keyword, qks, args);
        resultPage.getRecords()
//                .forEach(map -> sysDictionaryService.convertDictionary(map));
                .forEach(map -> {
                    if (map.get("fileId") != null) {
                        DataFile dataFile = dataFileMapper.selectById(map.get("fileId").toString());
                        map.put("fileName", dataFile.getFileName());
                    }
                });
        // 查询数据
        return resultPage;
    }

    @Override
    public List<Map<String, Object>> getFieldByType(String dataTypeId) {
        // 查询表头
        DataGeneral general = getById(dataTypeId);

        List<Map<String, Object>> cols = commonMapper.getCols(general.getTableName());

        for (Map<String, Object> col : cols) {
            String columnName = col.get("columnName").toString();
            String underscoreToCamel = StringUtil.underscoreToCamel(columnName);
            col.put("columnName", underscoreToCamel);
        }
        return cols;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveData(BasicDataDTO basicDataDTO) {
        // 获取表名等信息
        DataGeneral general = getById(basicDataDTO.getDataTypeId());
        String idStr = IdWorker.getIdStr();
        Map<String, Object> data = basicDataDTO.getData();
        data.put("id", idStr);

        Map<String, Object> underMap = keyCamelToUnderline(data);
        commonMapper.save(general.getTableName(), underMap);
    }

    private Map<String, Object> keyCamelToUnderline(Map<String, Object> data) {
        Map<String, Object> underMap = new HashMap<>();
        // 转换成下划线
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            String key = entry.getKey();
            String underline = com.baomidou.mybatisplus.core.toolkit.StringUtils.camelToUnderline(key);

            underMap.put(underline, entry.getValue());
        }
        return underMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteData(String dataTypeId, String dataId) {
        // 获取表名等信息
        DataGeneral general = getById(dataTypeId);
        commonMapper.delete(general.getTableName(), Collections.singletonList(dataId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateData(BasicDataDTO basicDataDTO) {
        // 获取表名等信息
        DataGeneral general = getById(basicDataDTO.getDataTypeId());

        Map<String, Object> data = keyCamelToUnderline(basicDataDTO.getData());
        commonMapper.update(general.getTableName(), data, basicDataDTO.getDataId());
    }

    @Override
    public Map<String, Object> getDataDetail(String dataTypeId, String dataId) {
        // 获取表名等信息
        DataGeneral general = getById(dataTypeId);
        Map<String, Object> result = commonMapper.getOne(general.getTableName(), dataId);
        return result;
    }

    @Override
    public List<DataGeneralDTO> getType(Integer areaType) {
        List<Integer> types = mappingType.get(areaType);
        if (CollUtil.isEmpty(types)) {
            throw new ServiceException("参数错误，未查询到该资源域类型");
        }
        return list(Wrappers.<DataGeneral>lambdaQuery().in(DataGeneral::getDataType, types))
                .stream()
                .map(DataGeneral::convert)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, IPage<Map<String, Object>>> queryIdleSource(IPage page, String areaId) {
        List<DataGeneralAreaRelation> relations = dataGeneralAreaRelationMapper.selectList(Wrappers.<DataGeneralAreaRelation>lambdaQuery()
                .eq(org.apache.commons.lang3.StringUtils.isNotBlank(areaId), DataGeneralAreaRelation::getAreaId, areaId));

        List<String> generalIds = relations.stream()
                .map(DataGeneralAreaRelation::getGeneralId)
                .collect(Collectors.toList());

        Collection<DataGeneral> dataGenerals = list(Wrappers.<DataGeneral>lambdaQuery().in(DataGeneral::getId, generalIds)
                .in(DataGeneral::getDataType, mappingType.get(1)));

        Map<String, IPage<Map<String, Object>>> result = new HashMap<>();
        // 判断资源是否被占用
        for (DataGeneral dataGeneral : dataGenerals) {
            Map<String, Object> params = new HashMap<>();
            params.put("area_id", areaId);
            IPage<Map<String, Object>> data = commonMapper.getData(page, dataGeneral.getTableName(), null, null, params);
            List<Map<String, Object>> records = data.getRecords();

            for (Map<String, Object> record : records) {
                // 查询每个表中的空闲资源
                DataEquipmentOccupancy occupancy = dataEquipmentOccupancyService.getOne(Wrappers.<DataEquipmentOccupancy>lambdaQuery().eq(DataEquipmentOccupancy::getAreaId, areaId)
                        .eq(DataEquipmentOccupancy::getGeneralId, record.get("general_id")).eq(DataEquipmentOccupancy::getEquipmentId, record.get("id"))
                        .ge(DataEquipmentOccupancy::getEndTime, LocalDateTime.now())
                        .le(DataEquipmentOccupancy::getStartTime, LocalDateTime.now()));

                record.put("generalName", dataGeneral.getTableComment());

                // 如果不为空则表示该资源被占用了, 1 true  0 false
                if (occupancy != null) {
                    record.put("isIdle", 1);
                } else {
                    record.put("isIdle", 0);
                }
            }
            Page<Map<String, Object>> copy = new Page<>();
            BeanUtil.copyProperties(data, copy);
            String key = dataGeneral.getDataType().toString();
            IPage<Map<String, Object>> value = result.get(key);
            if (value != null) {
                value.getRecords().addAll(records);
            } else {
                result.put(dataGeneral.getDataType().toString(), copy);
            }

        }
        return result;
    }

    @Override
    public List<DataGeneralDTO> queryEquipmentType(Integer type) {
        List<Integer> dataTypes = mappingType.get(type);
        List<DataGeneral> generals = list(Wrappers.<DataGeneral>lambdaQuery().in(DataGeneral::getDataType, dataTypes));
        return generals.stream()
                .map(DataGeneral::convert)
                .collect(Collectors.toList());
    }

    @Override
    public IPage<Map<String, Object>> pageEquipmentByType(IPage page, Integer type, String areaId, Integer category) {
        List<Integer> types;
        if (type == null) {
            // 如果不传type则需要根据分类来查询是资源还是目标
            types = mappingType.get(category);
        } else {
            types = Arrays.asList(type);
        }

        // 查询该类型下所有表
        List<DataGeneral> generals = dataGeneralMapper.selectList(Wrappers.<DataGeneral>lambdaQuery().in(DataGeneral::getDataType, types));

        // 根据表名去重
        List<DataGeneral> targetTables = new ArrayList<>(generals.stream()
                .collect(Collectors.toMap(
                        DataGeneral::getTableName,
                        Function.identity(),
                        (e, r) -> e
                ))
                .values());

        List<Map<String, Object>> combinedRecords = new ArrayList<>();

        Map<String, Object> args = new HashMap<>();
        // 查询参数
        if (org.apache.commons.lang3.StringUtils.isNotBlank(areaId)) {
            args.put("area_id", areaId);
        }

        int total = 0;
        int pageSize = (int) page.getSize();
        int currentPage = (int) page.getCurrent();

        // 获取每个表的数据并合并到一个列表中
        for (DataGeneral general : targetTables) {
            // 查询每个表的总记录数
            Integer count = commonMapper.getCount(general.getTableName(), args);
            total += count;

            if (count > 0) {
                // 查询所有数据
                IPage<Map<String, Object>> data = commonMapper.getData(new Page<>(1, count), general.getTableName(), null, null, args);
                List<Map<String, Object>> records = data.getRecords();
                records.forEach(map -> map.put("dataType", general.getDataType()));

                combinedRecords.addAll(records);
            }
        }

        // 对合并后的数据进行排序，假设按 "id" 字段排序
        combinedRecords.sort(Comparator.comparing(record -> (Comparable) record.get("id")));

        // 计算分页
        int fromIndex = (currentPage - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, combinedRecords.size());

        // 获取分页数据
        List<Map<String, Object>> pageRecords;
        if (fromIndex < combinedRecords.size()) {
            pageRecords = combinedRecords.subList(fromIndex, toIndex);
        } else {
            pageRecords = Collections.emptyList();
        }

        // 设置分页数据
        IPage<Map<String, Object>> resultPage = new Page<>(currentPage, pageSize, total);
        resultPage.setRecords(pageRecords);

        return resultPage;
    }

    @Override
    public List<DataGeneral> queryLittleType(Integer type) {
        List<Integer> types = mappingType.get(type);
        return list(Wrappers.<DataGeneral>lambdaQuery().in(DataGeneral::getDataType, types));
    }


//    @Override
//    public IPage<Map<String, Object>> pageEquipmentByType(IPage page, Integer type, String areaId, Integer category) {
//        List<Integer> types;
//        if (type == null) {
//            // 如果不传type则需要根据分类来查询是资源还是目标
//            types = mappingType.get(category);
//        } else {
//            types = Arrays.asList(type);
//        }
//        // 查询该类型下所有表
//        List<DataGeneral> generals = dataGeneralMapper.selectList(Wrappers.<DataGeneral>lambdaQuery().in(DataGeneral::getDataType, types));
//
//        // 根据表名去重
//        List<DataGeneral> targetTables = new ArrayList<>(generals.stream()
//                .collect(Collectors.toMap(
//                        DataGeneral::getTableName,
//                        Function.identity(),
//                        (e, r) -> e
//                ))
//                .values());
//
//        List<Map<String, Object>> combinedRecords = new ArrayList<>();
//
//        Map<String, Object> args = new HashMap<>();
//        // 查询参数
//        if (org.apache.commons.lang3.StringUtils.isNotBlank(areaId)) {
//            args.put("area_id", areaId);
//        }
//
//        int total = 0;
//        int pageSize = (int) page.getSize();
//        int currentPage = (int) page.getCurrent();
//
//        // 获取每个表的数据并合并到一个列表中
//        for (DataGeneral general : targetTables) {
//            // 查询每个表的总记录数
//            Integer count = commonMapper.getCount(general.getTableName(), args);
//            total += count;
//
//            // 计算需要从每个表中获取的数据量
//            int fetchSizePerTable = Math.min(pageSize, count);
//            if (fetchSizePerTable <= 0) {
//                continue;
//            }
//
//            // 分页查询数据
//            IPage<Map<String, Object>> data = commonMapper.getData(new Page<>(currentPage, fetchSizePerTable), general.getTableName(), null, null, args);
//            List<Map<String, Object>> records = data.getRecords();
//            records.forEach(map -> map.put("dataType", general.getDataType()));
//
//            combinedRecords.addAll(data.getRecords());
//        }
//
//        // 对合并后的数据进行排序，假设按 "id" 字段排序
//        combinedRecords.sort(Comparator.comparing(record -> (Comparable) record.get("id")));
//
//        // 计算分页
//        int fromIndex = (currentPage - 1) * pageSize;
//        int toIndex = Math.min(fromIndex + pageSize, combinedRecords.size());
//
//        // 获取分页数据
//        List<Map<String, Object>> pageRecords;
//        if (fromIndex < combinedRecords.size()) {
//            pageRecords = combinedRecords.subList(fromIndex, toIndex);
//        } else {
//            pageRecords = combinedRecords;
//        }
//
//        // 设置分页数据
//        IPage<Map<String, Object>> resultPage = new Page<>(currentPage, pageSize, total);
//        resultPage.setRecords(pageRecords);
//
//        return resultPage;
//    }

    @Override
    public Object pageStatsEquipment(IPage page, String generaId) {
        DataGeneral dataGeneral = getById(generaId);

        IPage data = commonMapper.getData(page, dataGeneral.getTableName(), null, null, null);

        List<Map<String, Object>> records = data.getRecords();

        for (Map<String, Object> record : records) {
            // 查询资源域名称
            String areaId = record.get("areaId").toString();
            DataArea area = dataAreaService.getById(areaId);
            record.put("areaName", area.getAreaName());

            // 查询被调用次数
            List<TaskTargetRelation> targetRelations = taskTargetRelationService.list(Wrappers.<TaskTargetRelation>lambdaQuery().eq(TaskTargetRelation::getTargetId, record.get("id")));
            record.put("totalCount", targetRelations.size());

            // 未执行的任务数量
            List<TaskTargetRelation> unExecuteList = targetRelations.stream()
                    .filter(taskTargetRelation -> taskTargetRelation.getStartTime() != null && taskTargetRelation.getStartTime().isAfter(LocalDateTime.now()))
                    .collect(Collectors.toList());

            record.put("unExecuteCount", unExecuteList.size());
        }
        return data;
    }

}
