package com.gy.show.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.common.ServiceException;
import com.gy.show.entity.dos.*;
import com.gy.show.entity.dto.RequirementFileDTO;
import com.gy.show.entity.dto.RequirementInfoDTO;
import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.entity.dto.TaskTargetRelationDTO;
import com.gy.show.enums.*;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.mapper.RequirementInfoMapper;
import com.gy.show.service.*;
import com.gy.show.util.GISUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RequirementInfoServiceImpl extends ServiceImpl<RequirementInfoMapper, RequirementInfo> implements RequirementInfoService {

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Autowired
    private TaskTargetService taskTargetService;

    @Autowired
    private FileService fileService;

    @Autowired
    private DataPresetTrackService dataPresetTrackService;

    @Autowired
    private DataGeneralService dataGeneralService;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private TaskTargetRelationService taskTargetRelationService;

    @Autowired
    private RequirementTargetTrackService requirementTargetTrackService;

    @Autowired
    private SysDictionaryService sysDictionaryService;

    @Autowired
    private OpsService opsService;

    @Autowired
    private DataPresetTrackInfoService dataPresetTrackInfoService;

    @Autowired
    private SysConfigService sysConfigService;

    @Override
    public RequirementInfoDTO getRequirementInfoById(String id) {
        RequirementInfo info = getById(id);
        RequirementInfoDTO infoDTO = null;
        if (info != null) {
            infoDTO = info.convert();
            String fileId = info.getFileId();
            if (StringUtils.isNotBlank(fileId)) {
                DataFile file = fileService.getById(fileId);
                infoDTO.setFile(file);
            }
        } else {
            throw new ServiceException("参数异常");
        }

        // 查询需求关联目标表
        List<TaskTargetRelation> taskTargetRelations = taskTargetRelationService.list(Wrappers.<TaskTargetRelation>lambdaQuery().eq(TaskTargetRelation::getRequirementId, id));

        if (CollUtil.isNotEmpty(taskTargetRelations)) {
            List<RequirementInfoDTO.TargetInfo> targetInfos = new ArrayList<>();
            for (TaskTargetRelation taskTargetRelation : taskTargetRelations) {
                String relationId = taskTargetRelation.getId();
                RequirementInfoDTO.TargetInfo targetInfo = new RequirementInfoDTO.TargetInfo();
                BeanUtil.copyProperties(taskTargetRelation, targetInfo);

                targetInfo.setTrackStartTime(taskTargetRelation.getStartTime());
                targetInfo.setTrackEndTime(taskTargetRelation.getEndTime());

                List<RequirementTask> taskList = requirementTaskService.list(Wrappers.<RequirementTask>lambdaQuery().eq(RequirementTask::getTargetRelationId, relationId));

                List<RequirementTaskDTO> taskDTOS = taskList.stream()
                        .map(RequirementTask::convert)
                        .collect(Collectors.toList());

                targetInfo.setTasks(taskDTOS);

                targetInfos.add(targetInfo);
            }

            infoDTO.setTargetInfos(targetInfos);
        }

        return infoDTO;
    }

    @Override
    public RequirementInfo createRequirementInfo(RequirementInfo requirementInfo) {
        return null;
    }

    @Override
    public RequirementInfo updateRequirementInfo(String id, RequirementInfo newRequirementInfo) {
        return null;
    }

    @Override
    public void deleteRequirementInfo(String id) {
        // 删除需求
        removeById(id);

        // 删除关联目标
        List<TaskTargetRelation> targetRelations = taskTargetRelationService.list(Wrappers.<TaskTargetRelation>lambdaQuery().eq(TaskTargetRelation::getRequirementId, id));
        for (TaskTargetRelation targetRelation : targetRelations) {
            taskTargetRelationService.removeById(targetRelation.getId());

            // 删除目标航迹
            requirementTargetTrackService.remove(Wrappers.<RequirementTargetTrack>lambdaQuery().eq(RequirementTargetTrack::getRelationId, targetRelation.getId()));
        }

        // 删除目标下的任务
        requirementTaskService.remove(Wrappers.<RequirementTask>lambdaQuery().eq(RequirementTask::getRequirementId, id));
    }

    @Override
    public IPage<RequirementInfo> getAllRequirementInfos(int page, int size, String keyword) {
        return page(new Page<>(page, size), Wrappers.<RequirementInfo>lambdaQuery()
                .like(StringUtils.isNotBlank(keyword), RequirementInfo::getRequirementName, keyword)
                .orderByDesc(RequirementInfo::getCreateTime));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RequirementInfo addRequirementInfo(RequirementInfoDTO requirementInfoDTO) {
        RequirementInfo info = requirementInfoDTO.convert();
        // 存储需求表
        save(info);

        // 需求自动分解
        requirementInfoDTO.setId(info.getId());
        requirementDecompose(requirementInfoDTO);

        return info;
    }

    @Override
    public Object getTaskById(String requirementId) {
        // 查询已分解的任务列表
        List<RequirementTask> tasks = requirementTaskService.list(Wrappers.<RequirementTask>lambdaQuery()
                .eq(RequirementTask::getRequirementId, requirementId));

        List<RequirementTaskDTO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(tasks)) {
            List<String> taskIds = tasks.stream()
                    .map(RequirementTask::getId)
                    .collect(Collectors.toList());

            Collection<TaskTargetRelation> relations = taskTargetService.listByIds(taskIds);

            Map<String, List<TaskTargetRelationDTO>> groupByTaskId = relations.stream()
                    .map(TaskTargetRelation::convert)
                    .collect(Collectors.groupingBy(TaskTargetRelationDTO::getTaskId));

            result = tasks.stream()
                    .map(task -> {
                        RequirementTaskDTO taskDTO = task.convert();
                        // 如果查询为空则说明该任务没有关联目标
                        List<TaskTargetRelationDTO> targets = groupByTaskId.get(taskDTO.getId());
                        taskDTO.setTarget(CollUtil.isNotEmpty(targets) ? targets.get(0) : null);
                        return taskDTO;
                    })
                    .collect(Collectors.toList());

        }

        return result;
    }

    @Override
    public InputStreamResource getFileTemplate(HttpServletResponse response) {
        // 创建需求信息数据
        List<Map<String, Object>> demandInfo = new LinkedList<>();
        Map<String, Object> demandRow = new LinkedHashMap<>();
        demandRow.put("需求ID", 1);
        demandRow.put("需求名称", "请输入需求名称");
        demandRow.put("需求类型", "随遇接入任务/预规划任务");
        demandRow.put("重要程度", "一般/重要");
        demandRow.put("需求开始时间", "2024-07-09 00:00:00");
        demandRow.put("需求结束时间", "2024-07-09 18:21:53");
        demandRow.put("需求描述", "请输入需求描述");
        demandInfo.add(demandRow);

        // 创建目标信息数据
        List<Map<String, Object>> targetInfo = new LinkedList<>();
        Map<String, Object> targetRow1 = new LinkedHashMap<>();
        targetRow1.put("目标ID", 1);
        targetRow1.put("需求ID", 1);
        targetRow1.put("目标名称", "P-3C");
        targetRow1.put("目标类型", "无人机/无人车/无人艇/弹");
        targetRow1.put("业务类型", "遥控/遥测/测量/数传");
        targetRow1.put("所属资源域", "西南域");
        targetInfo.add(targetRow1);

        Map<String, Object> targetRow2 = new LinkedHashMap<>();
        targetRow2.put("目标ID", 2);
        targetRow2.put("需求ID", 1);
        targetRow2.put("目标名称", "F14");
        targetRow2.put("目标类型", "无人机/无人车/无人艇/弹");
        targetRow2.put("业务类型", "遥控/遥测/测量/数传");
        targetRow2.put("所属资源域", "东北域");
        targetInfo.add(targetRow2);

        // 创建目标轨迹信息数据
        List<Map<String, Object>> trajectoryInfo = new LinkedList<>();
        Map<String, Object> trajectoryRow1 = new LinkedHashMap<>();
        trajectoryRow1.put("坐标ID", 1);
        trajectoryRow1.put("目标ID", 1);
        trajectoryRow1.put("经度", 96.63959);
        trajectoryRow1.put("维度", 35.23893);
        trajectoryRow1.put("高度", 0);
        trajectoryRow1.put("方向", 0);
        trajectoryRow1.put("速度", "2024-07-09 18:21:53");
        trajectoryInfo.add(trajectoryRow1);

        Map<String, Object> trajectoryRow2 = new LinkedHashMap<>();
        trajectoryRow2.put("坐标ID", 2);
        trajectoryRow2.put("目标ID", 1);
        trajectoryRow2.put("经度", 96.63959);
        trajectoryRow2.put("维度", 35.23893);
        trajectoryRow2.put("高度", 0);
        trajectoryRow2.put("方向", 0);
        trajectoryRow2.put("速度", "2024-07-09 18:21:54");
        trajectoryInfo.add(trajectoryRow2);

        Map<String, Object> trajectoryRow3 = new LinkedHashMap<>();
        trajectoryRow3.put("坐标ID", 3);
        trajectoryRow3.put("目标ID", 1);
        trajectoryRow3.put("经度", 96.63959);
        trajectoryRow3.put("维度", 35.23893);
        trajectoryRow3.put("高度", 0);
        trajectoryRow3.put("方向", 0);
        trajectoryRow3.put("速度", "2024-07-09 18:21:55");
        trajectoryInfo.add(trajectoryRow3);

        // 创建Excel文件并写入数据
        ExcelWriter writer = ExcelUtil.getWriter("需求模板.xlsx");

        writer.setSheet(0);
        writer.getWorkbook().removeSheetAt(0);

        // 写入需求信息Sheet
        writer.setSheet("需求信息");
        writer.write(demandInfo, true);
        writer.autoSizeColumnAll();

        // 写入目标信息Sheet
        writer.setSheet("目标信息");
        writer.write(targetInfo, true);

        // 写入目标轨迹信息Sheet
        writer.setSheet("目标轨迹信息");
        writer.write(trajectoryInfo, true);


        ByteArrayOutputStream out = new ByteArrayOutputStream();
        writer.flush(out);

        ByteArrayInputStream inputStream = new ByteArrayInputStream(out.toByteArray());
        // 关闭writer，释放内存
        writer.close();

        log.info("Excel模板生成成功！");
        return new InputStreamResource(inputStream);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void parseExcel(String id) {
        Map<String, RequirementFileDTO> requirementFile = opsService.getRequirementFile();

        if (CollUtil.isEmpty(requirementFile)) {
            throw new ServiceException("参数错误");
        }
        RequirementFileDTO requirementFileDTO = requirementFile.get(id);
        // 读取需求描述
        ExcelReader requirementReader = ExcelUtil.getReader(requirementFileDTO.getFilePath(), 0);
        List<Map<String, Object>> requirements = requirementReader.readAll();

        // 读取目标信息
        ExcelReader targetReader = ExcelUtil.getReader(requirementFileDTO.getFilePath(), 1);
        List<Map<String, Object>> targets = targetReader.readAll();

        // 读取坐标点
        ExcelReader coordinateReader = ExcelUtil.getReader(requirementFileDTO.getFilePath(), 2);
        List<Map<String, Object>> coordinates = coordinateReader.readAll();

        // 读取执行 任务信息
        ExcelReader taskReader = ExcelUtil.getReader(requirementFileDTO.getFilePath(), 3);
        List<Map<String, Object>> tasks = taskReader.readAll();

        // 获取字典
        Map<String, List<SysDictionary>> requirementTypeDic = getDicGroupByValue("requirementType");
        Map<String, List<SysDictionary>> importanceDic = getDicGroupByValue("importance");
        Map<String, List<SysDictionary>> sourceTypeDic = getDicGroupByValue("sourceType");
        Map<String, List<SysDictionary>> businessTypeDic = getDicGroupByValue("businessType");

        List<Map<String, Object>> result = new ArrayList<>();
        // 遍历解析
        for (Map<String, Object> requirement : requirements) {
            String requirementId = ((Long) requirement.get("需求ID")).toString();
            String requirementName = (String) requirement.get("需求名称");
            String requirementType = (String) requirement.get("需求类型");
            String importance = (String) requirement.get("重要程度");
            String startTime = (String) requirement.get("需求开始时间");
            String endTime = (String) requirement.get("需求结束时间");
            String description = (String) requirement.get("需求描述");

            RequirementInfo requirementInfo = new RequirementInfo();
            requirementInfo.setRequirementName(requirementName);
            requirementInfo.setRequirementType(requirementTypeDic.get(requirementType).get(0).getDictValue());
            requirementInfo.setImportance(importanceDic.get(importance).get(0).getDictValue());
            requirementInfo.setStartTime(DateUtil.parseLocalDateTime(startTime));
            requirementInfo.setEndTime(DateUtil.parseLocalDateTime(endTime));
            requirementInfo.setRequirementComment(description);
            requirementInfo.setSource(RequirementSourceEnum.EXTERNAL.getCode());

            checkRequirementInfo(requirementInfo);

            // 保存需求信息，后面需要用需求ID
            save(requirementInfo);
//            RequirementInfoDTO requirementInfoDTO = requirementInfo.convert();

            log.info("提取需求成功，需求名称:{}", requirementName);

            for (Map<String, Object> target : targets) {
                // 判断目标是否该需求下的
                if (requirementId.equals(((Long) target.get("需求ID")).toString())) {
                    String targetId = target.get("目标ID").toString();
                    String targetName = (String) target.get("目标名称");
                    String targetType = (String) target.get("目标类型");
                    LocalDateTime trackStartTime = DateUtil.parseLocalDateTime(target.get("轨迹开始时间").toString());
//                    LocalDateTime trackEndTime = DateUtil.parseLocalDateTime(target.get("轨迹结束时间").toString());
//                    String bzType = (String) target.get("业务类型");

                    log.info("开始解析目标：{}", targetName);

                    SysDictionary dictionary = sourceTypeDic.get(targetType).get(0);
                    // 查询主表获取目标的表名
                    DataGeneral general = dataGeneralService.getOne(Wrappers.<DataGeneral>lambdaQuery()
                            .eq(DataGeneral::getDataType, dictionary.getDictValue()));

                    // 根据目标名称去查询该目标是否存在，存在则直接使用，不存在则新增
                    Map<String, Object> args = new HashMap<>();
                    args.put("name", targetName);
                    IPage<Map<String, Object>> data = commonMapper.getData(new Page<>(), general.getTableName(), null, null, args);
                    if (data.getRecords().size() == 0) {
                        throw new ServiceException("该文件中的目标名称不存在");
                    }

                    String realTargetId;
                    TaskTargetRelation targetRelation;
                    if (CollUtil.isNotEmpty(data.getRecords())) {
                        Map<String, Object> targetMap = data.getRecords().get(0);
                        realTargetId = targetMap.get("id").toString();

                        RequirementInfoDTO.TargetInfo targetInfo = new RequirementInfoDTO.TargetInfo();
                        targetInfo.setTargetId(realTargetId);
                        targetInfo.setTargetName(targetName);
                        targetInfo.setGeneralId(targetMap.get("generalId").toString());
                        targetInfo.setTrackStartTime(trackStartTime);
//                        targetInfo.setTrackEndTime(trackEndTime);

                        // 解析目标航迹
                        Map<String, String> presetTrackMap = parseTrackCoordinates(coordinates);

                        String presetId = presetTrackMap.get(targetId);
                        targetInfo.setTrackPresetId(presetId);

                        // 创建任务目标关联表
                        targetRelation = createTarget(requirementInfo.getId(), targetInfo);


                        // 解析任务
                        List<RequirementTaskDTO> taskDTOS = parseTask(tasks, targetId);
                        targetInfo.setTasks(taskDTOS);

                        // 创建航迹
                        createTrack(targetRelation.getId(), targetInfo);

                        // 创建任务
                        createTask(requirementInfo.convert(), targetInfo, targetRelation.getId());


                    } else {
                        log.warn("该目标{}数据库中不存在，请输入正确的目标名称", targetName);
                        continue;
                    }
                    log.info("需求：{}，任务：{}解析完成，准备开始轨迹解析", requirementName, targetName);

                } else {
                    log.info("该目标不属于本次需求：{}", target.get("目标名称"));
                }
            }
        }

        // 解析完后删除文件
        requirementFile.remove(id);
    }

    private void checkRequirementInfo(RequirementInfo requirementInfo) {
        // 结束时间不能小于开始时间
        if (requirementInfo.getEndTime().isBefore(requirementInfo.getStartTime())) {
            throw new ServiceException("需求的结束时间不能大于开始时间");
        }

        if (StringUtils.isBlank(requirementInfo.getRequirementName())) {
            throw new ServiceException("需求名称不能为空");
        }
    }

    @Override
    public void deleteRequirementFile(String id) {
        Map<String, RequirementFileDTO> requirementFile = opsService.getRequirementFile();
        RequirementFileDTO requirementFileDTO = requirementFile.get(id);

        Path path = Paths.get(requirementFileDTO.getFilePath());
        if (Files.exists(path)) {
            try {
                Files.delete(path);
                log.info("文件删除成功，文件路径：{}", path);
            } catch (IOException e) {
                log.error("文件删除失败", e);
                throw new RuntimeException(e);
            }
        } else {
            log.error("文件不存在, 文件路径：{}", path);
        }
        requirementFile.remove(id);
    }

    private List<RequirementTaskDTO> parseTask(List<Map<String, Object>> tasks, String targetId) {
        List<RequirementTaskDTO> taskDTOS = new ArrayList<>(tasks.size());
        for (Map<String, Object> task : tasks) {
            String taskTargetId = task.get("目标ID").toString();
            if (targetId.equals(taskTargetId)) {
                RequirementTaskDTO taskDTO = new RequirementTaskDTO();

                String taskName = (String) task.get("任务名称");
                String taskType = (String) task.get("任务类型");
                String taskStartTime = (String) task.get("任务开始时间");
                String taskEndTime = (String) task.get("任务结束时间");
                String repeatType = (String) task.get("是否重复");

                taskDTO.setTaskName(taskName);
                taskDTO.setTaskType(Collections.singletonList(TaskTypeEnum.getEnumByName(taskType).getCode()));
                taskDTO.setStartTime(DateUtil.parseLocalDateTime(taskStartTime));
                taskDTO.setEndTime(DateUtil.parseLocalDateTime(taskEndTime));
                taskDTO.setRepeatType(RepeatTypeEnum.getEnumByName(repeatType).getCode());

                taskDTOS.add(taskDTO);
            }
        }
        return taskDTOS;
    }

    /**
     * 将解析的航迹先存入预设航迹表中
     * @param coordinates
     */
    private Map<String, String> parseTrackCoordinates(List<Map<String, Object>> coordinates) {
        // k -> targetId v -> presetId
        Map<String, String> presetTrackMap = new HashMap<>();
        DataPresetTrack currentEndPoint;

        Map<String, List<Map<String, Object>>> groupByName = coordinates.stream()
                .collect(Collectors.groupingBy(m -> m.get("轨迹名称").toString()));

        for (Map.Entry<String, List<Map<String, Object>>> entry : groupByName.entrySet()) {
            DataPresetTrack currentStartPoint = null;
            List<Map<String, Object>> values = entry.getValue();

            DataPresetTrackInfo trackInfo = new DataPresetTrackInfo();
            trackInfo.setName(entry.getKey());
            trackInfo.setType(TrackTypeEnum.EXTERNAL.getCode());

            DataPresetTrackInfo existTrack = dataPresetTrackInfoService.getOne(Wrappers.<DataPresetTrackInfo>lambdaQuery().eq(DataPresetTrackInfo::getName, entry.getKey()));
            String presetTrackId;
            // 如果没有则新建
            if (existTrack == null) {
                dataPresetTrackInfoService.save(trackInfo);
                presetTrackId = trackInfo.getId();

                for (Map<String, Object> coordinate : values) {
                    String targetId = coordinate.get("目标ID").toString();

                    Double longitude = Double.parseDouble(coordinate.get("经度").toString());
                    Double latitude = Double.parseDouble(coordinate.get("纬度").toString());
                    Double altitude = Double.parseDouble(coordinate.get("高度").toString());
                    Double speed = Double.parseDouble(coordinate.get("速度").toString());

                    DataPresetTrack track = new DataPresetTrack();
                    track.setPresetId(presetTrackId);
                    track.setLongitude(BigDecimal.valueOf(longitude));
                    track.setLatitude(BigDecimal.valueOf(latitude));
                    track.setAltitude(BigDecimal.valueOf(altitude));
                    track.setSpeed(BigDecimal.valueOf(speed));
                    track.setTime(0);

                    // 参数校验
                    checkCoordinate(track);

                    // 根据经纬度和速度计算时间
                    Integer timePoint = 0;
                    if (currentStartPoint == null) {
                        currentStartPoint = track;
                    } else {
                        currentEndPoint = track;
                        timePoint = calculateTrackTime(currentStartPoint, currentEndPoint, currentStartPoint.getSpeed());
                        log.info("航迹速度计算时间点结果：{}", timePoint);
                    }

                    track.setTime(currentStartPoint.getTime() + timePoint);
                    currentStartPoint = track;

                    dataPresetTrackService.save(track);

                    presetTrackMap.put(targetId, presetTrackId);
                }
            } else {
                Map<String, Object> coordinate = values.get(0);
                String targetId = coordinate.get("目标ID").toString();
                presetTrackId = existTrack.getId();

                presetTrackMap.put(targetId, presetTrackId);
            }
        }

        return presetTrackMap;
    }

    private static void checkCoordinate(DataPresetTrack track) {
        if (track.getLatitude().compareTo(new BigDecimal(90)) > 0 ||
                track.getLatitude().compareTo(new BigDecimal(-90)) < 0) {
            throw new ServiceException("请输入正确的经纬度信息");
        }

        if (track.getLongitude().compareTo(new BigDecimal(180)) > 0 ||
                track.getLongitude().compareTo(new BigDecimal(-180)) < 0) {
            throw new ServiceException("请输入正确的经纬度信息");
        }
    }

    private Integer calculateTrackTime(DataPresetTrack currentStartPoint, DataPresetTrack currentEndPoint, BigDecimal speed) {
        double distance = GISUtils.calculateDistance(currentStartPoint.getLatitude().doubleValue(), currentStartPoint.getLongitude().doubleValue(),
                currentEndPoint.getLatitude().doubleValue(), currentEndPoint.getLongitude().doubleValue());

        double hour = distance / speed.doubleValue();

        return Math.toIntExact(Math.round(hour * 3600));
    }

    private Map<String, List<SysDictionary>> getDicGroupByValue(String requirementType) {
        List<SysDictionary> requirementTypeDic = sysDictionaryService.getDicByType(requirementType);
        return requirementTypeDic.stream()
                .collect(Collectors.groupingBy(SysDictionary::getDictName));
    }

    /**
     * 需求自动分解
     * @param info
     */
    private void requirementDecompose(RequirementInfoDTO info) {
        if (info.getIsDeCompose() == 1) {
            // 根据目标去拆解任务
            List<RequirementInfoDTO.TargetInfo> targetInfos = info.getTargetInfos();
            for (RequirementInfoDTO.TargetInfo targetInfo : targetInfos) {
                // 参数校验
                parameterCheck(targetInfo, info);

                // 创建目标
                TaskTargetRelation target = createTarget(info.getId(), targetInfo);

                // 创建航迹
                createTrack(target.getId(), targetInfo);

                // 创建任务
                createTask(info, targetInfo, target.getId());
            }
        } else {
            log.info("该需求任务无需自动拆解:{}", info.getRequirementName());
        }
    }

    private void parameterCheck(RequirementInfoDTO.TargetInfo targetInfo, RequirementInfoDTO info) {
        LocalDateTime requirementStartTime = info.getStartTime();
        if (targetInfo.getTrackStartTime().isBefore(requirementStartTime)) {
            throw new ServiceException("轨迹开始时间不能在需求时间之前");
        }

        List<RequirementInfoDTO.TargetInfo> targetInfos = info.getTargetInfos();
        for (RequirementInfoDTO.TargetInfo targetInfo1 : targetInfos) {
            for (RequirementTaskDTO task : targetInfo1.getTasks()) {
                // 检查任务开始和结束时间是否在需求时间范围内
                if (task.getStartTime().isBefore(info.getStartTime()) || task.getEndTime().isAfter(info.getEndTime())) {
                    throw new ServiceException("任务的开始时间或结束时间不能超出需求时间");
                }

                // 检查任务结束时间是否大于开始时间
                if (task.getEndTime().isBefore(task.getStartTime())) {
                    throw new ServiceException("任务的结束时间不能小于开始时间");
                }
            }
            // 判断任务中是否有重复’
            Map<String, Long> duplicates = targetInfo1.getTasks().stream().collect(Collectors.groupingBy(
                    task -> task.getTaskType().toString() + task.getStartTime() + task.getEndTime(),
                    Collectors.counting()
            ));

            duplicates.forEach((key, count) -> {
                if (count > 1) {
                    throw new ServiceException("存在相同任务类型及时间，请检查参数");
                }
            });
        }

    }

    private void createTrack(String relationId, RequirementInfoDTO.TargetInfo targetInfo) {
        List<DataPresetTrack> tracks = dataPresetTrackService.list(Wrappers.<DataPresetTrack>lambdaQuery().eq(DataPresetTrack::getPresetId, targetInfo.getTrackPresetId()));

        List<RequirementTargetTrack> targetTracks = tracks.stream()
                .map(track -> {
                    RequirementTargetTrack targetTrack = new RequirementTargetTrack();
                    BeanUtil.copyProperties(track, targetTrack);
                    targetTrack.setRelationId(relationId);
                    targetTrack.setId(null);
                    return targetTrack;
                }).collect(Collectors.toList());

        requirementTargetTrackService.saveBatch(targetTracks);
    }

    private TaskTargetRelation createTarget(String requirementId, RequirementInfoDTO.TargetInfo targetInfo) {
        TaskTargetRelation relation = new TaskTargetRelation();
        relation.setRequirementId(requirementId);

        DataGeneral dataGeneral = dataGeneralService.getById(targetInfo.getGeneralId());
        Map<String, Object> target = commonMapper.getOne(dataGeneral.getTableName(), targetInfo.getTargetId());

        // 目标没有航迹，获取预设航迹的第一个点为起始点
        List<DataPresetTrack> tracks = dataPresetTrackService.list(Wrappers.<DataPresetTrack>lambdaQuery()
                .eq(DataPresetTrack::getPresetId, targetInfo.getTrackPresetId()).orderByAsc(DataPresetTrack::getTime));

        if (CollUtil.isEmpty(tracks)) {
            throw new ServiceException("该航迹不存在或已被删除");
        }

        if (CollUtil.isNotEmpty(tracks)) {
            DataPresetTrack dataPresetTrack = tracks.get(0);

            relation.setLongitude(dataPresetTrack.getLongitude());
            relation.setLatitude(dataPresetTrack.getLatitude());
            relation.setAltitude(dataPresetTrack.getAltitude());
        }
        // 获取航迹最后一个点的时间，用来计算结束时间
        DataPresetTrack endPoint = tracks.get(tracks.size() - 1);

        // 设置航迹开始和结束时间
        relation.setStartTime(targetInfo.getTrackStartTime());
        relation.setEndTime(targetInfo.getTrackStartTime().plusSeconds(endPoint.getTime()));
        targetInfo.setTrackEndTime(targetInfo.getTrackStartTime().plusSeconds(endPoint.getTime()));

        relation.setGeneralId(targetInfo.getGeneralId());
        relation.setTargetId(targetInfo.getTargetId());
        relation.setName(target.get("name").toString());

        relation.setType(1); // 目标

        taskTargetRelationService.save(relation);

        return relation;
    }

    private void createTask(RequirementInfoDTO requirementInfo, RequirementInfoDTO.TargetInfo targetInfo, String targetRelationId) {
        // 参数校验
        requirementInfo.setTargetInfos(Arrays.asList(targetInfo));
        parameterCheck(targetInfo, requirementInfo);

        List<RequirementTaskDTO> tasks = targetInfo.getTasks();
        if (CollUtil.isNotEmpty(tasks)) {
            // 默认取1就是时间颗粒度的值
            SysConfig sysConfig = sysConfigService.getById(1);

            String interval = sysConfig.getValue();
            int idx = 0;
            for (RequirementTaskDTO taskDTO : tasks) {
                List<Integer> taskTypes = taskDTO.getTaskType();
                for (Integer taskType : taskTypes) {
                    idx++;

                    // 任务时间不能超过航迹时间
                    if (taskDTO.getStartTime().isBefore(targetInfo.getTrackStartTime()) || taskDTO.getEndTime().isAfter(targetInfo.getTrackEndTime())) {
                        throw new ServiceException("任务的开始或结束时间不能超过航迹开始结束时间");
                    }
                    RequirementTask task = taskDTO.convert();
                    task.setId(null);
                    task.setRequirementId(requirementInfo.getId());
                    task.setTargetRelationId(targetRelationId);
                    task.setImportance(requirementInfo.getImportance()); // 2024.09.24 ADD, 任务继承需求的重要程度

                    // 设置任务名称
                    StringBuilder taskName = new StringBuilder(requirementInfo.getRequirementName());
                    taskName.append("-")
                            .append(targetInfo.getTargetName())
                            .append("-子任务-")
                            .append(idx);
                    task.setTaskName(taskName.toString());
                    task.setTaskType(taskType);

                    // 任务拆解
//                    List<RequirementTask> dismantleTask = dismantleTask(task, interval);

                    // 如果该任务为周期任务则需要创建整个周期的任务
                    if (!taskDTO.getRepeatType().equals(RepeatTypeEnum.ONCE.getCode())) {
                        if (requirementInfo.getRequirementType().equals(ScheduleTypeEnum.LJ.getCode())) {
                            throw new ServiceException("随遇接入任务无法选择重复类型");
                        }
                        List<RequirementTask> repeatTask = createRepeatTask(task, requirementInfo.getStartTime(), requirementInfo.getEndTime());

                        requirementTaskService.saveBatch(repeatTask);

                        log.info("周期任务创建完成，总计：{}个任务", repeatTask.size());
//                        for (RequirementTask requirementTask : task) {
//                        }
                    } else {
                        requirementTaskService.save(task);
                    }

                }
            }
        }

    }

    private List<RequirementTask> dismantleTask(RequirementTask task, String interval) {
        List<RequirementTask> segments = new ArrayList<>();
        LocalDateTime startTime = task.getStartTime();
        LocalDateTime endTime = task.getEndTime();
        LocalDateTime current = startTime;

        Duration granularity = Duration.ofHours(Long.parseLong(interval));

        while (current.isBefore(endTime)) {
            LocalDateTime next = current.plus(granularity);

            if (next.isAfter(endTime)) {
                next = endTime;
            }

            RequirementTask newTask = new RequirementTask();
            // 复制基础属性
            BeanUtil.copyProperties(task, newTask);
            newTask.setStartTime(current);
            newTask.setEndTime(next);
            newTask.setId(null);

            segments.add(newTask);
            current = next;
        }
        log.info("任务拆解完成，拆解颗粒度：{}小时，拆解数量：{}", interval, segments.size());

        return segments;
    }

    // 创建周期任务
    private List<RequirementTask> createRepeatTask(RequirementTask task, LocalDateTime startTime, LocalDateTime endTime) {
        List<RequirementTask> tasks = new ArrayList<>();
        LocalDateTime currentStart = task.getStartTime();
        LocalDateTime currentEnd = task.getEndTime();

        // 获取任务关联的目标实例化对象
        TaskTargetRelation taskTargetRelation = taskTargetRelationService.getById(task.getTargetRelationId());
        LocalDateTime trackStartTime = taskTargetRelation.getStartTime();
        LocalDateTime trackEndTime = taskTargetRelation.getEndTime();

        // 查询航迹
        List<RequirementTargetTrack> trackList = requirementTargetTrackService.queryTrackByIds(Arrays.asList(task.getTargetRelationId()));

        while (currentStart.isBefore(endTime) && currentEnd.isBefore(endTime)) {
            if (currentStart.isAfter(startTime) || currentStart.isEqual(startTime)) {
                RequirementTask newTask = new RequirementTask();
                BeanUtil.copyProperties(task, newTask);

                // 首先创建关联目标
                if (trackStartTime.isAfter(taskTargetRelation.getStartTime())) {
                    TaskTargetRelation targetRelation = new TaskTargetRelation();
                    BeanUtil.copyProperties(taskTargetRelation, targetRelation);

                    targetRelation.setStartTime(trackStartTime);
                    targetRelation.setEndTime(trackEndTime);
                    targetRelation.setId(null);

                    taskTargetRelationService.save(targetRelation);

                    newTask.setTargetRelationId(targetRelation.getId());

                    // 航迹也需要创建一份, 修改ID和关联主键后可以直接保存
                    trackList.forEach(track -> {
                        track.setRelationId(targetRelation.getId());
                        track.setId(null);
                    });

                    requirementTargetTrackService.saveBatch(trackList);

                }

                newTask.setStartTime(currentStart);
                newTask.setEndTime(currentEnd);
                newTask.setId(null);
                tasks.add(newTask);
            }
            RepeatTypeEnum repeatTypeEnum = RepeatTypeEnum.getEnumByCode(task.getRepeatType());
            switch (repeatTypeEnum) {
                case DAILY:
                    currentStart = currentStart.plusDays(1);
                    currentEnd = currentEnd.plusDays(1);

                    trackStartTime = trackStartTime.plusDays(1);
                    trackEndTime = trackEndTime.plusDays(1);
                    break;
                case WEEKLY:
                    currentStart = currentStart.plusWeeks(1);
                    currentEnd = currentEnd.plusWeeks(1);
                    break;
                case MONTHLY:
                    currentStart = currentStart.plusMonths(1);
                    currentEnd = currentEnd.plusMonths(1);
                    break;
            }
        }
        return tasks;
    }
}
