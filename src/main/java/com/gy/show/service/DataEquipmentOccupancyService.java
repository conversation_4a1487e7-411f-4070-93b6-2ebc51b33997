package com.gy.show.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gy.show.entity.dos.DataEquipmentOccupancy;
import com.gy.show.entity.dto.DataEquipmentOccupancyDTO;

import java.util.List;
import java.util.Map;

public interface DataEquipmentOccupancyService extends IService<DataEquipmentOccupancy> {

    /**
     * 根据ID获取目标数据信息
     *
     * @param id 目标数据ID
     * @return 目标数据信息
     */
    DataEquipmentOccupancy getDataEquipmentOccupancyById(String id);

    /**
     * 创建目标数据信息
     *
     * @param dataEquipmentOccupancy 目标数据信息
     * @return 创建后的目标数据信息
     */
    DataEquipmentOccupancy createDataEquipmentOccupancy(DataEquipmentOccupancy dataEquipmentOccupancy);

    /**
     * 更新目标数据信息
     *
     * @param id                     目标数据ID
     * @param newDataEquipmentOccupancy 更新后的目标数据信息
     * @return 更新后的目标数据信息
     */
    DataEquipmentOccupancy updateDataEquipmentOccupancy(String id, DataEquipmentOccupancy newDataEquipmentOccupancy);

    /**
     * 删除目标数据信息
     *
     * @param id 目标数据ID
     */
    void deleteDataEquipmentOccupancy(String id);

    /**
     * 分页获取所有目标数据信息
     *
     * @param page    当前页码，默认为1
     * @param size    每页记录数，默认为10
     * @param keyword 关键字，模糊查询任务ID或设备模型ID
     * @return 目标数据信息分页列表
     */
    IPage<DataEquipmentOccupancy> getAllDataEquipmentOccupancies(int page, int size, String keyword);

    Map<String, Object> getOccupancyByArea(String areaId);

    IPage<DataEquipmentOccupancyDTO> getEquipmentByTask(int page, int size, String taskId);

    void deleteByTaskId(String taskId);

    Object getDataByEquipment(String equipmentId, String requirementId);

    List<DataEquipmentOccupancy> queryEquipmentByTaskIds(List<String> taskIds);
}
