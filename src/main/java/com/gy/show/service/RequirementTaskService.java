package com.gy.show.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.entity.vo.TaskQueryVO;
import com.gy.show.enums.TaskScheduleStatusEnum;

import java.time.LocalDateTime;
import java.util.List;

public interface RequirementTaskService extends IService<RequirementTask> {

    RequirementTask addRequirementTask(RequirementTaskDTO requirementTaskDTO);

    void updateRequirementTask(RequirementTaskDTO requirementTaskDTO);

    void submitTask(String requirementId);

    List<RequirementTask> getTaskByRequirement(String id);

    Object queryTaskByEquipment(String generalId, String equipmentId);

    List<RequirementTaskDTO> queryScheduleTask();

    void changeTaskStatus(List<RequirementTaskDTO> tasks, TaskScheduleStatusEnum taskStatusEnum);

    IPage getAllRequirementTasks(IPage page, TaskQueryVO taskQueryVO);

    RequirementTaskDTO getRequirementTaskById(String id);

    Object getDataByTarget(String targetRelationId);
}
