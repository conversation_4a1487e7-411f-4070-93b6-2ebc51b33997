package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.dto.AlgorithmDTO;
import com.gy.show.entity.dto.CalculateCoverDTO;
import com.gy.show.entity.dto.ConfirmScheduleDTO;
import com.gy.show.entity.dto.ScheduleTaskDTO;
import com.gy.show.service.ScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Api(tags = "资源调度相关接口")
@RestController
@RequestMapping("/schedule")
public class ScheduleController extends BaseController {

    @Autowired
    private ScheduleService scheduleService;

    @ApiOperation("确认资源调度结果")
    @PostMapping("/confirmSchedule")
    public Result confirmSchedule(@RequestBody List<ConfirmScheduleDTO> confirmScheduleDTO) {
        scheduleService.confirmSchedule(confirmScheduleDTO);
        return Result.ok();
    }

    @ApiOperation("基础调度算法")
    @PostMapping("/algo")
    public Result invokeAlgorithm(@RequestBody @Valid AlgorithmDTO basicAlgorithmDTO) {
        Object result = scheduleService.invokeAlgorithm(basicAlgorithmDTO, getPage());
        return Result.ok(result);
    }

    @ApiOperation("根据当前选中的设备计算设备覆盖率")
    @PostMapping("/calculateCover")
    public Result calculateCover(@RequestBody CalculateCoverDTO calculateCoverDTO) {
        Object result = scheduleService.calculateCover(calculateCoverDTO);
        return Result.ok(result);
    }

    @ApiOperation("下发调度结果")
    @PostMapping("/sendResult")
    public Result sendResult(@Valid @RequestBody ScheduleTaskDTO scheduleTaskDTO) {
        scheduleService.sendResult(scheduleTaskDTO);
        return Result.ok();
    }

    @ApiOperation("二次协商")
    @PostMapping("/renegotiate")
    public Result renegotiate(@Valid @RequestBody ScheduleTaskDTO scheduleTaskDTO) {
        scheduleService.renegotiate(scheduleTaskDTO);
        return Result.ok();
    }

}
