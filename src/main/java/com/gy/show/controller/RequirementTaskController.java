package com.gy.show.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gy.show.common.Result;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.entity.vo.TaskQueryVO;
import com.gy.show.service.RequirementTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/task")
@Api(tags = "任务管理")
public class RequirementTaskController extends BaseController {

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Value("${schedule.fixed-rate}")
    private Integer fixedRate;

    @GetMapping("/{id}")
    @ApiOperation("根据ID获取任务")
    public Result getRequirementTaskById(@PathVariable String id) {
        RequirementTaskDTO requirementTask = requirementTaskService.getRequirementTaskById(id);
        return Result.ok(requirementTask);
    }

    @PostMapping
    @ApiOperation("新增任务")
    public Result addRequirementTask(@RequestBody RequirementTaskDTO requirementTaskDTO) {
        RequirementTask task = requirementTaskService.addRequirementTask(requirementTaskDTO);
        return Result.ok(task);
    }

    @PutMapping
    @ApiOperation("更新任务")
    public Result updateRequirementTask(@RequestBody RequirementTaskDTO requirementTaskDTO) {
        requirementTaskService.updateRequirementTask(requirementTaskDTO);
        return Result.ok();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除任务")
    public Result deleteRequirementTask(@PathVariable String id) {
        requirementTaskService.removeById(id);
        return Result.ok();
    }

    @PostMapping("/list")
    @ApiOperation("分页查询任务")
    public Result listRequirementTasks(@RequestBody TaskQueryVO taskQueryVO) {
        IPage result = requirementTaskService.getAllRequirementTasks(getPage(), taskQueryVO);
        return Result.ok(result);
    }

    @ApiOperation("提交需求任务分解结果，主要用于状态变更")
    @PostMapping("/submitTask")
    public Result submitTask(@RequestParam("requirementId") String requirementId) {
        requirementTaskService.submitTask(requirementId);
        return Result.ok();
    }

    @ApiOperation("根据资源查询资源关联的任务信息")
    @GetMapping("/queryTaskByEquipment")
    public Result queryTaskByEquipment(@RequestParam("generalId") String generalId,
                                       @RequestParam("equipmentId") String equipmentId) {
        Object result = requirementTaskService.queryTaskByEquipment(generalId, equipmentId);
        return Result.ok(result);
    }

    @ApiOperation("查询周期任务的待调度列表，包含待调度和调度中的任务")
    @GetMapping("/queryScheduleTask")
    public Result queryScheduleTask() {
        List<RequirementTaskDTO> result = requirementTaskService.queryScheduleTask();
        return Result.ok(result);
    }

    @ApiOperation("查询当前周期任务定时轮询时间")
    @GetMapping("/queryPollingTime")
    public Result queryPollingTime() {
        return Result.ok(fixedRate);
    }

    @GetMapping("/getDataByTarget")
    @ApiModelProperty("根据目标查询任务")
    public Result getDataByTarget(@RequestParam("targetRelationId") String targetRelationId) {
        Object result = requirementTaskService.getDataByTarget(targetRelationId);
        return Result.ok(result);
    }

}
