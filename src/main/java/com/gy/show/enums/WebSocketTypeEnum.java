package com.gy.show.enums;

import java.util.Arrays;
import java.util.Objects;

public enum WebSocketTypeEnum {


    PROCESS(1, "态势模拟推演进度推送"),

    SATELLITE(2, "卫星"),

    SCHEDULE(3, "一级资源调度"),

    FILE(4, "文件上传提醒"),

    FULLVIEW(5, "全景态势");


    private Integer code;

    private String message;

    WebSocketTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static WebSocketTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(WebSocketTypeEnum.values()).filter(f -> Objects.equals(f.getCode(), code)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
