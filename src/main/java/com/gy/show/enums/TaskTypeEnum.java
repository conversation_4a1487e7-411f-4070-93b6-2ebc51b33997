package com.gy.show.enums;

import java.util.Arrays;

public enum TaskTypeEnum {


    YK(1, "遥控"),

    YC(2, "遥测"),

    CL(3, "测量"),

    SC(4, "数传");


    private Integer code;

    private String message;

    TaskTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static TaskTypeEnum getEnumByName(String name) {
        return Arrays.stream(TaskTypeEnum.values()).filter(f -> f.getMessage().equals(name)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
