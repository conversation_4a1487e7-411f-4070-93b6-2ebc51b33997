package com.gy.show.enums;

import java.util.Arrays;

public enum RepeatTypeEnum {


    ONCE(1, "仅一次"),

    DAILY(2, "每日重复"),

    WEEKLY(3, "每周重复"),

    MONTHLY(4, "每月重复");


    private Integer code;

    private String message;

    RepeatTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static RepeatTypeEnum getEnumByName(String name) {
        return Arrays.stream(RepeatTypeEnum.values()).filter(f -> f.getMessage().equals(name)).findFirst().orElse(null);
    }

    public static RepeatTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(RepeatTypeEnum.values()).filter(f -> f.getCode().equals(code)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
