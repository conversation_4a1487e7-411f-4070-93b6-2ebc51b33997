package com.gy.show.entity.dto;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.DataEquipmentOccupancy;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class ConfirmScheduleDTO {

    /**
     * 需求ID
     */
//    private String requirementId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 确认需要使用的资源列表
     */
    private List<EquipmentDTO> equipments;

    @Data
    public static class EquipmentDTO extends ObjectConvert<DataEquipmentOccupancy> {

        /**
         * 主表ID
         */
        private String generalId;

        /**
         * 资源ID
         */
        private String equipmentId;

        private String name;

        private BigDecimal longitude;

        private BigDecimal latitude;

        private BigDecimal altitude;

        /**
         * 资源域ID
         */
        private String areaId;

        /**
         * 资源类型，属于天地空哪种
         */
        private String type;

        /**
         * 使用资源开始时间
         */
        private LocalDateTime startTime;

        /**
         * 使用资源结束时间
         */
        private LocalDateTime endTime;

        private BigDecimal coverRate;


    }

}

