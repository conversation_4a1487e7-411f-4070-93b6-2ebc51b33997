package com.gy.show.entity.dto;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.DataFile;
import com.gy.show.entity.dos.RequirementInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> 需求信息表
 */
@Data
public class RequirementInfoDTO extends ObjectConvert<RequirementInfo> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 需求名称
     */
    private String requirementName;

    /**
     * 需求类型
     */
    private Integer requirementType;

    /**
     * 调度类型 1 即时任务 2 周期任务
     */
    private Integer scheduleType;

    /**
     * 需求来源 1 手动创建 2 外部输入
     */
    private Integer source;

    /**
     * 重要程度
     */
    private Integer importance;

    /**
     * 调度状态 0 未调度 1 已调度
     */
    private Integer status;

    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 需求描述
     */
    private String requirementComment;

    /**
     * 预设资源
     */
    private List<String> presetSource;

    private DataFile file;

    /**
     * 轨迹文件
     */
    private String trackFileId;

    /**
     * 是否自动分解
     */
    @ApiModelProperty("是否自动分解 1 是 0 否")
    private Integer isDeCompose;

    /**
     * 目标信息集合
     */
    @NotEmpty(message = "至少为任务选择一个目标")
    private List<TargetInfo> targetInfos;

    private static final long serialVersionUID = 1L;

    @Data
    public static class TargetInfo {

        /**
         * 目标ID
         */
        private String targetId;

        private String targetName;

        /**
         * 主表ID
         */
        private String generalId;

        /**
         * 预设航迹ID
         */
        private String trackPresetId;

        /**
         * 航迹开始时间
         */
        private LocalDateTime trackStartTime;

        /**
         * 航迹结束时间
         */
        private LocalDateTime trackEndTime;

        /**
         * 任务列表
         */
        private List<RequirementTaskDTO> tasks;
    }
}