package com.gy.show.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

@Data
public class ScheduleTaskDTO {

    /**
     * 待调度的资源列表
     */
//    @NotEmpty(message = "下发调度资源列表不能为空")
    private List<Map<String, Object>> equipments;

    private List<Map<String, Object>> areas;

    @ApiModelProperty("1 一级 2 二级")
    private Integer type;

    private String clientId;
}
