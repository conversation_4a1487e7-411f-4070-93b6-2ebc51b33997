package com.gy.show.entity.dos;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dto.RequirementInfoDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 需求信息表
 */
@Data
public class RequirementInfo extends ObjectConvert<RequirementInfoDTO> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 需求名称
     */
    private String requirementName;

    /**
     * 需求类型
     */
    private Integer requirementType;

    /**
     * 调度类型 1 即时任务 2 周期任务
     */
    private Integer scheduleType;

    /**
     * 需求来源 1 手动创建 2 外部输入
     */
    private Integer source;

    /**
     * 重要程度
     */
    private Integer importance;

    /**
     * 需求状态 0 待分解 1 已分解 1 已调度
     */
    private Integer status;

    /**
     * 文件ID
     */
    private String fileId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 需求描述
     */
    private String requirementComment;

    private static final long serialVersionUID = 1L;
}