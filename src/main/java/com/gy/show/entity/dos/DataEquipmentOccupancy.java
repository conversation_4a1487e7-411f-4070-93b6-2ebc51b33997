package com.gy.show.entity.dos;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dto.DataEquipmentOccupancyDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> 目标数据表
 */
@Data
public class DataEquipmentOccupancy extends ObjectConvert<DataEquipmentOccupancyDTO> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 主表ID
     */
    private String generalId;

    /**
     * 资源域ID
     */
    private String areaId;

    /**
     * 设备模型ID
     */
    private String equipmentId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否激活
     */
    private Byte isActive;

    private BigDecimal coverRate;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DataEquipmentOccupancy that = (DataEquipmentOccupancy) o;
        return Objects.equals(equipmentId, that.equipmentId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(equipmentId);
    }
}