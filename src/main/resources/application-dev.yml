# ---------------------------- 数据库配置 ---------------------------------- #
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************************
#    url: *******************************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      connection-test-query: SELECT 1 FROM DUAL
      connection-timeout: 6000000000
      maximum-pool-size: 500
      max-lifetime: 1800000
      minimum-idle: 20
      validation-timeout: 3000
      idle-timeout: 60000
    redis:
      host: 127.0.0.1
      port: 6379
      password:
  mybatis:
    mapper-locations: classpath:mapper/*.xml

# ---------------------------- mybatisPlus配置 ---------------------------- #
mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml
  #  configuration:
  #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      insert-strategy: not_null
      update-strategy: not_null
      id-type: id_worker_str

# ---------------------------- 系统参数配置 ---------------------------- #
schedule:
  fixed-rate: 360000 # 一个小时，单位秒
file:
#  path: E:\ZQY\KY\temp\
  path: D:\zyf\temp\wrpt\
logging:
  level:
    com.gy.show: debug
